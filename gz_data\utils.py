import pandas as pd
import numpy as np
from sklearn.metrics import mean_squared_error
import matplotlib.pyplot as plt
import glob
import os

# --- 从您提供的 G-ATSM.py 中引用的配置和数据加载函数 ---
# (假设 G-ATSM.py 在同一目录下或者已安装为库)
# 如果 G-ATSM.py 中的函数有修改，请确保这里的调用是同步的

# --- Configuration (部分来自 G-ATSM.py) ---
DATA_PATH_PATTERN = "gz_spot_select_*.csv"  # 确保您的CSV文件在此路径
YIELD_IS_PERCENTAGE = True  # 收益率是否以百分比表示
CHINESE_FONT = 'SimHei' # 用于matplotlib中文显示

# --- 1. Load and Prepare Data (简化版，依赖 G-ATSM.py 或类似功能) ---
def load_and_prepare_data_for_modeling(path_pattern,
                                       date_col='date',
                                       maturity_col='maturity',
                                       yield_col='yield',
                                       yield_is_percentage=True,
                                       resample_freq='M',
                                       selected_maturities=None): # 新增参数，用于选择特定期限
    """
    加载多个CSV文件，合并，并将长格式数据转换为宽格式。
    与G-ATSM.py中的函数类似，但增加了对特定期限选择的支持，并简化了部分输出。
    """
    all_files = glob.glob(path_pattern)
    all_files.sort()

    if not all_files:
        raise FileNotFoundError(f"未找到匹配模式的文件: {path_pattern}")

    df_list = []
    print(f"信息：正在读取 {len(all_files)} 个文件...")
    for filename in all_files:
        try:
            df_temp = pd.read_csv(filename)
            df_list.append(df_temp)
        except Exception as e:
            print(f"读取文件 {filename} 时出错: {e}")
            continue

    if not df_list:
        raise ValueError("没有加载任何数据。请检查CSV文件和模式。")

    full_df = pd.concat(df_list, ignore_index=True)

    try:
        full_df[date_col] = pd.to_datetime(full_df[date_col])
    except Exception as e:
        print(f"错误：转换 '{date_col}' 列为日期格式时出错: {e}")
        return pd.DataFrame()

    if yield_is_percentage:
        full_df[yield_col] = full_df[yield_col] / 100.0

    full_df = full_df.sort_values(by=[date_col, maturity_col])
    yield_panel = full_df.pivot_table(index=date_col, columns=maturity_col, values=yield_col)

    if resample_freq:
        yield_panel_resampled = yield_panel.resample(resample_freq).last()
    else: # 如果不重采样，直接使用原始频率（可能需要额外处理，这里假设按月）
        yield_panel_resampled = yield_panel

    yield_panel_resampled = yield_panel_resampled.ffill().bfill()
    yield_panel_resampled = yield_panel_resampled.dropna(axis=0, how='any')
    yield_panel_resampled = yield_panel_resampled.dropna(axis=1, how='all')

    print(f"信息：重采样后收益率面板形状: {yield_panel_resampled.shape}")
    if yield_panel_resampled.empty:
        raise ValueError("处理后的收益率面板为空。")

    # 选择特定期限进行建模
    if selected_maturities:
        available_maturities = [m for m in selected_maturities if m in yield_panel_resampled.columns]
        missing_maturities = [m for m in selected_maturities if m not in yield_panel_resampled.columns]
        if missing_maturities:
            print(f"警告：以下目标期限在数据中缺失，将不会被包含: {missing_maturities}")
        if not available_maturities:
            print(f"警告：所有选定的目标期限均不在数据中。将使用所有可用期限。")
            # Fallback to all available if none of the selected are present
            # Or raise error: raise ValueError("None of the selected maturities are available in the data.")
        else:
            yield_panel_resampled = yield_panel_resampled[available_maturities]
            print(f"信息：已筛选出以下期限进行建模: {available_maturities}")
    else: # 如果未指定，使用所有可用期限
        print(f"信息：使用所有可用期限进行建模: {yield_panel_resampled.columns.tolist()}")


    return yield_panel_resampled

# --- 评估指标 ---
def calculate_oos_r_squared(y_true, y_pred_model, y_pred_benchmark):
    """
    计算样本外 R-squared (OOS R²)。
    公式: OOS R² = 1 - (sum((y_true - y_pred_model)^2) / sum((y_true - y_pred_benchmark)^2))
    在我们的案例中，随机游走的预测是 yt-h，所以 (y_true - y_pred_benchmark) 就是 yt - yt-h，即真实变化值。
    而随机游走模型预测的变化值为0，所以分母也可以是 sum(y_true^2) 如果 y_true 是指 真实的变化值 Δyt。

    根据研报公式：OOSR2 = 1 - (sum((Y_hat_t - y_t)^2) / sum(y_t^2))
    其中 y_t 是真实值 (即 Δyt), Y_hat_t 是预测值。
    随机游走模型认为未来变化值为零，即 Y_hat_t_rw = 0。
    则 MSE_rw = sum((0 - y_t)^2) / T = sum(y_t^2) / T
    MSE_model = sum((Y_hat_t_model - y_t)^2) / T
    OOS R^2 = 1 - MSE_model / MSE_rw
    """
    mse_model = np.mean((y_true - y_pred_model)**2)
    mse_benchmark = np.mean((y_true - 0)**2) # 随机游走预测变化为0，所以 y_pred_benchmark (for changes) is 0
    if mse_benchmark == 0: # 避免除以零
        return -np.inf if mse_model > 0 else (0 if mse_model == 0 else np.inf)
    return 1 - (mse_model / mse_benchmark)

def calculate_hit_ratio(y_true_change, y_pred_change):
    """计算方向预测准确率 (Hit Ratio)"""
    correct_direction = np.sign(y_true_change) == np.sign(y_pred_change)
    # 处理0的情况，如果真实变化为0，预测为0也算对
    correct_direction[(y_true_change == 0) & (y_pred_change == 0)] = True
    # 如果真实变化为0，预测非0，算错；如果真实变化非0，预测为0，也算错（除非特别定义）
    # 这里简化：如果真实变化为0，任何非0预测都是方向错误（除非绝对值很小）
    # 或者更严格：只有同号或都为0才算对。np.sign(0) is 0.
    return np.mean(correct_direction)

def diebold_mariano_test(errors1, errors2, h=1, crit="MSE"):
    """
    执行 Diebold-Mariano 检验。
    H0: 两个模型的预测精度相同。
    HA: 两个模型的预测精度不同。

    参数:
    errors1: 第一个模型的预测误差序列 (真实值 - 预测值)。
    errors2: 第二个模型的预测误差序列 (真实值 - 预测值)。
    h: 预测期数 (影响自相关的调整)。对于1步预测，通常h=1。
       对于h步预测，自相关最多到h-1阶。
    crit: "MSE", "MAD", "MAPE" (这里主要用MSE)。

    返回:
    dm_stat: DM统计量。
    p_value: p值。
    """
    errors1 = np.asarray(errors1)
    errors2 = np.asarray(errors2)

    if crit == "MSE":
        d = errors1**2 - errors2**2
    elif crit == "MAD":
        d = np.abs(errors1) - np.abs(errors2)
    else:
        raise ValueError("Criterion not supported")

    d_mean = np.mean(d)
    n = len(d)

    # 计算d的方差 (HAC robust variance)
    # 使用Newey-West估计调整自相关
    gamma = np.zeros(h) # Autocovariances
    for k in range(h):
        gamma[k] = np.sum((d[k:] - d_mean) * (d[:n-k] - d_mean)) / n

    var_d = gamma[0] + 2 * np.sum(gamma[1:])
    if var_d <= 0: # 可能因为样本小或序列特性导致非正方差
        print("警告: Diebold-Mariano检验中d的HAC方差非正，可能导致结果不可靠。返回NaN。")
        return np.nan, np.nan

    dm_stat = d_mean / np.sqrt(var_d / n)

    from scipy.stats import norm
    p_value = 2 * (1 - norm.cdf(np.abs(dm_stat))) # 双边检验

    return dm_stat, p_value

# 中文显示设置
try:
    plt.rcParams['font.sans-serif'] = [CHINESE_FONT]
    plt.rcParams['axes.unicode_minus'] = False
except Exception as e:
    print(f"警告：设置中文字体 '{CHINESE_FONT}' 失败: {e}")

# 创建虚拟数据的功能 (如果gz_spot_select_*.csv不存在)
def create_dummy_data_if_not_exists(path_pattern="gz_spot_select_*.csv", current_dir="."):
    if not glob.glob(os.path.join(current_dir, path_pattern)):
        print("未找到数据文件。正在创建测试数据用于演示...")
        base_year = 2007 # 开始年份
        num_years = 15   # 创建15年的数据
        data_frames = []

        for i in range(num_years):
            year = base_year + i
            # 为每一年创建一些日期
            dates_this_year = pd.date_range(start=f'{year}-01-01', end=f'{year}-12-31', freq='B') # 工作日
            # 每隔10个交易日取一个观察值，模拟研报中的半月采样
            dates_sampled = dates_this_year[::10]

            maturities = [0.25, 0.5, 0.75, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10] # 与TARGET_MATURITIES一致
            data = []
            for date_val in dates_sampled:
                for mat_val in maturities:
                    # 简化的收益率生成逻辑
                    base_yield = 2.0 + np.sin( (date_val.year - base_year + date_val.month/12) * np.pi / 5) # 年度周期性
                    slope_effect = 0.3 * mat_val
                    noise = np.random.normal(0, 0.1)
                    yield_val = base_yield + slope_effect + (date_val.dayofyear / 365 - 0.5) * 0.2 + noise
                    data.append({'date': date_val.strftime('%Y/%m/%d'), 'maturity': mat_val, 'yield': max(0.1, yield_val)}) # 确保收益率>0

            df_dummy = pd.DataFrame(data)
            dummy_filename = os.path.join(current_dir, f"gz_spot_select_{year}.csv")
            df_dummy.to_csv(dummy_filename, index=False)
            data_frames.append(df_dummy)
            print(f"已创建测试数据文件: {dummy_filename}")
        return pd.concat(data_frames, ignore_index=True)
    return None # 如果文件已存在，不创建