import pandas as pd
import os

# 定义年份范围
years = range(2007, 2021)

for year in years:
    # 输入文件名
    input_file = f'gkz_spot_raw_{year}.csv'
    
    # 检查文件是否存在
    if not os.path.exists(input_file):
        print(f"文件 {input_file} 不存在，跳过此年。")
        continue
    
    # 读取原始数据
    data = pd.read_csv(input_file)
    
    # 删除 yield 为 0 或空值 的行
    filtered_yield_data = data[(data['yield'] != 0) & (~data['yield'].isna())]
    
    # 构建第一个输出文件名
    output_file_raw = f'gkz_spot_{year}.csv'
    
    # 保存去除了 yield=0 的数据
    filtered_yield_data.to_csv(output_file_raw, index=False)
    
    # 再次筛选出 maturity 在0到10之间的记录
    selected_data = filtered_yield_data[(filtered_yield_data['maturity'] >= 0) & (filtered_yield_data['maturity'] <= 10)]
    
    # 构建第二个输出文件名
    output_file_selected = f'gkz_spot_{year}.csv'
    
    # 保存选中的数据
    selected_data.to_csv(output_file_selected, index=False)
    
    print(f"已完成处理: {year} 年")

print("所有年份处理完成！")
