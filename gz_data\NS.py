import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
# from scipy.optimize import minimize # 不再需要 minimize
from sklearn.linear_model import LinearRegression # 导入线性回归模型
import statsmodels.api as sm # 也可以使用 statsmodels

# NS模型函数 (保持不变)
def nelson_siegel(tau, a, b, c, lam=0.59776):
    # Calculate the factors for b and c parameters
    # Factor for b: (1 - exp(-lam*tau)) / (lam*tau)
    # Handle tau=0 case to avoid division by zero, limit is 1
    factor_b = np.ones_like(tau) # Initialize with ones for tau=0 case
    non_zero_tau_mask = tau != 0
    tau_nz = tau[non_zero_tau_mask]
    factor_b[non_zero_tau_mask] = (1 - np.exp(-lam * tau_nz)) / (lam * tau_nz)

    # Factor for c: exp(-lam*tau)
    factor_c = np.exp(-lam * tau)

    # Calculate the NS yield
    term1 = a
    term2 = factor_b * b
    term3 = factor_c * c
    return term1 + term2 + term3

# 使用 OLS 线性回归估计参数
def estimate_ns_params_ols(yields, maturities, lam=0.59776):
    """
    Estimates NS model parameters (a, b, c) using OLS regression
    for a fixed lambda.
    Args:
        yields (np.array): Observed yields.
        maturities (np.array): Corresponding maturities.
        lam (float): Fixed lambda parameter.
    Returns:
        np.array: Estimated parameters [a, b, c].
        Returns [np.nan, np.nan, np.nan] if regression fails or insufficient data.
    """
    # Ensure inputs are numpy arrays
    tau = np.asarray(maturities)
    y = np.asarray(yields)

    # Check for sufficient data points (at least 3 for 3 parameters)
    if len(tau) < 3:
        print(f"Warning: Insufficient data points ({len(tau)}) for OLS estimation. Need at least 3.")
        return np.array([np.nan, np.nan, np.nan])

    # Calculate the factors (regressors) for b and c
    # Factor for b: (1 - exp(-lam*tau)) / (lam*tau)
    factor_b = np.ones_like(tau) # Initialize with ones for tau=0 case
    non_zero_tau_mask = tau != 0
    tau_nz = tau[non_zero_tau_mask]
    factor_b[non_zero_tau_mask] = (1 - np.exp(-lam * tau_nz)) / (lam * tau_nz)

    # Factor for c: exp(-lam*tau)
    factor_c = np.exp(-lam * tau)

    # Create the design matrix (regressors) X
    # The model is y = a*1 + b*factor_b + c*factor_c
    # The constant term corresponds to 'a'
    X = np.column_stack([factor_b, factor_c])
    # Add a constant column for the intercept 'a' using statsmodels
    X_sm = sm.add_constant(X, prepend=True) # Adds constant at the beginning

    # Perform OLS regression using statsmodels
    try:
        model = sm.OLS(y, X_sm)
        results = model.fit()
        # The parameters are [const (a), factor_b coeff (b), factor_c coeff (c)]
        # Note: The paper's formula has c multiplied by exp(-lambda*tau),
        # while Diebold & Li (2006) often use c * [(1 - exp(-lam*tau))/(lam*tau) - exp(-lam*tau)]
        # Here we follow the formula in the provided paper: y = a + b*factor_b + c*factor_c
        # So, results.params correspond to [a, b, c]
        return results.params
    except Exception as e:
        print(f"Error during OLS estimation: {e}")
        return np.array([np.nan, np.nan, np.nan])

# --- Main script part ---

# Dates for comparison plots
dates_to_plot = ['2009-11-26', '2013-06-20', '2014-11-10', '2015-05-28']

ns_params_all_years = []

# Loop through years 2007 to 2020
for year in range(2007, 2021):
    try:
        file_name = f'gz_spot_select_{year}.csv'
        print(f"Processing {file_name}...")
        df = pd.read_csv(file_name)

        # Convert yields column to decimal (divide by 100)
        # Ensure the column name matches your Excel file ('yeilds' or 'yields')
        yield_col_name = 'yeild' if 'yeild' in df.columns else 'yield'
        if yield_col_name not in df.columns:
            print(f"Yield column not found in {file_name}. Skipping.")
            continue
        df[yield_col_name] = df[yield_col_name] / 100

        # Ensure column names match your Excel file
        date_col_name = 'date' if 'date' in df.columns else 'date'
        maturity_col_name = 'maturity' if 'maturity' in df.columns else 'maturity'

        # 去掉maturity为0的数据
        df = df[df[maturity_col_name] != 0]

        if date_col_name not in df.columns or maturity_col_name not in df.columns:
            print(f"Required columns ('{date_col_name}', '{maturity_col_name}') not found in {file_name}. Skipping.")
            continue

        # Convert date column to datetime objects
        df[date_col_name] = pd.to_datetime(df[date_col_name])

        # Get unique maturities for the entire dataset (consider if this should be per-day)
        # Using unique maturities from the current year's file for the completeness check
        unique_maturities_in_file = df[maturity_col_name].unique()
        # Sort maturities for consistency
        unique_maturities_in_file.sort()

        # Estimate NS parameters for each date in the current year
        ns_params_current_year = []
        for date, group in df.groupby(date_col_name):
            group = group.sort_values(by=maturity_col_name) # Sort group by maturity
            group_maturities = group[maturity_col_name].values
            group_yields = group[yield_col_name].values

            # Check if the group contains data for all unique maturities found in this file
            # Or alternatively, check if there are enough data points (e.g., >= 3)
            # Using a check for minimum number of points (>=3) for robustness
            if len(group_maturities) >= 3:
            # Original check (might be too strict):
            # if len(group_maturities) == len(unique_maturities_in_file) and np.all(group_maturities == unique_maturities_in_file):
                params = estimate_ns_params_ols(group_yields, group_maturities) # Use OLS function
                if not np.isnan(params).any(): # Check if estimation was successful
                    ns_params_current_year.append([date] + list(params))
                else:
                    print(f"Parameter estimation failed for date {date}. Skipping.")
            else:
                # Optional: Log dates with incomplete data
                # print(f"Skipping date {date} due to incomplete maturity data.")
                pass

        # Create DataFrame for the current year's parameters
        if ns_params_current_year:
            ns_params_df = pd.DataFrame(ns_params_current_year, columns=["Date", "a_t", "b_t", "c_t"])
            ns_params_df.set_index("Date", inplace=True)
            ns_params_all_years.append(ns_params_df)

            # Plot comparison for specified dates if they exist in this year's data
            for date_str in dates_to_plot:
                date_dt = pd.to_datetime(date_str)
                if date_dt.year == year and date_dt in ns_params_df.index:
                    # Get actual data for plotting (ensure correct column names)
                    actual_data = df[df[date_col_name] == date_dt].sort_values(by=maturity_col_name)
                    actual_maturities = actual_data[maturity_col_name].values
                    actual_yields_plot = actual_data[yield_col_name].values

                    # Get fitted parameters
                    params_plot = ns_params_df.loc[date_dt][['a_t', 'b_t', 'c_t']].values

                    # Calculate fitted yields using the NS function
                    fitted_yields_plot = nelson_siegel(actual_maturities, *params_plot)

                    # Plotting
                    plt.figure(figsize=(10, 6))
                    plt.plot(actual_maturities, actual_yields_plot, 'o-', label=f"Actual Yield ({date_str})")
                    plt.plot(actual_maturities, fitted_yields_plot, '--', label=f"Fitted NS Model (OLS) ({date_str})")
                    plt.title(f"Comparison of Actual Yields and NS Model Fitted Yields ({date_str})")
                    plt.xlabel("Maturity (Years)")
                    plt.ylabel("Yield")
                    plt.legend()
                    plt.grid(True)
                    plt.xticks(rotation=45)
                    plt.tight_layout()
                    plt.show()
        else:
            print(f"No valid parameter estimations for year {year}.")


    except FileNotFoundError:
        print(f"File {file_name} not found. Skipping year {year}.")
    except Exception as e:
        print(f"An error occurred processing year {year}: {e}")

# Combine all parameters into a single DataFrame if list is not empty
if ns_params_all_years:
    ns_params_combined = pd.concat(ns_params_all_years)
    # Sort by date index
    ns_params_combined.sort_index(inplace=True)

    # Display some info about the combined parameters
    print("\nCombined NS Parameters (Head):")
    print(ns_params_combined.head())
    print("\nCombined NS Parameters (Tail):")
    print(ns_params_combined.tail())


    # Plotting parameters over time
    plt.figure(figsize=(12, 8))

    for col, label in zip(["a_t", "b_t", "c_t"], ["a_t (level)", "b_t (slope)", "c_t (curvature)"]):
        plt.plot(ns_params_combined.index, ns_params_combined[col], label=label)

    # Adjust y-axis ticks for better readability if needed
    # Example: plt.yticks(np.arange(ns_params_combined[["a_t", "b_t", "c_t"]].min().min() - 0.01,
    #                             ns_params_combined[["a_t", "b_t", "c_t"]].max().max() + 0.01, 0.01))

    plt.title("NS Model Parameters Over Time (2007-2020, Estimated via OLS)")
    plt.xlabel("Date")
    plt.ylabel("Parameter Value")
    plt.legend()
    plt.grid(True)
    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.show()

    # Display summary statistics
    print("\nSummary Statistics for Estimated Parameters:")
    print(ns_params_combined.describe())

else:
    print("\nNo parameter dataframes were generated.")
