import requests
import pandas as pd
from datetime import datetime, timedelta
import logging
import time

# 设置日志记录
logging.basicConfig(filename='bond_yield_crawler.log', level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def fetch_bond_yield(date_str):
    url = "https://yield.chinabond.com.cn/cbweb-mn/yc/searchYc"
    headers = {
        'Content-Type': 'application/json;charset=UTF-8',
        'Accept': '*/*',
        'Origin': 'https://yield.chinabond.com.cn',
        'X-Requested-With': 'XMLHttpRequest',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36 Edg/135.0.0.0'
    }
    data = {
        "xyzSelect": "txy",
        "workTimes": date_str,
        "dxbj": "0",
        "qxll": "1,",
        "yqqxN": "N",
        "yqqxK": "K",
        "ycDefIds": "8a8b2ca037a7ca910137bfaa94fa5057,",
        "wrjxCBFlag": "0",
        "locale": "zh_CN"
    }

    try:
        response = requests.post(url, headers=headers, json=data)
        response.raise_for_status()
        result = response.json()

        if not result['success']:
            logging.info(f"No yield curve data found for {date_str}")
            return None

        series_data = result['data'][0]['seriesData']
        df = pd.DataFrame(series_data, columns=['Maturity', 'Rate'])
        df['Date'] = date_str
        return df

    except requests.exceptions.RequestException as e:
        logging.error(f"Error fetching data for {date_str}: {e}")
        return None

def main():
    start_date = datetime(2021, 1, 1)
    end_date = datetime(2024, 12, 31)

    current_date = start_date
    while current_date <= end_date:
        year = current_date.year
        date_str = current_date.strftime('%Y-%m-%d')
        print(f"Fetching data for {date_str}...")
        
        df = fetch_bond_yield(date_str)
        if df is not None:
            csv_filename = f'bond_yields_{year}.csv'
            if current_date == start_date or not df.empty:
                if current_date.month == 1 and current_date.day == 1:
                    df.to_csv(csv_filename, index=False, mode='w', header=True)
                else:
                    df.to_csv(csv_filename, index=False, mode='a', header=False)
        
        # 添加休眠时间，避免请求过于频繁
        time.sleep(1)  # 每次请求后休眠1秒
        
        current_date += timedelta(days=1)

if __name__ == "__main__":
    main()



