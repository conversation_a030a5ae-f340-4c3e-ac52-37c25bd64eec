import pandas as pd
import numpy as np
import xgboost as xgb
import os
import statsmodels.api as sm
from sklearn.metrics import mean_squared_error

# Assuming utils.py is in the same directory
from utils import (load_and_prepare_data_for_modeling, calculate_oos_r_squared,
                   calculate_hit_ratio, diebold_mariano_test, create_dummy_data_if_not_exists,
                   DATA_PATH_PATTERN)

# --- Task-Specific Parameters ---
TASK2_TARGET_MATURITIES = [0.25, 1, 3, 5, 7, 10]
TASK2_HORIZONS_MONTHS = [12]
TASK3_HORIZONS_MONTHS = [12]
NELSON_SIEGEL_LAMBDA = 0.0609

# --- General Parameters & XGBoost Configuration ---
YIELD_IS_PERCENTAGE = True
OUTPUT_DIR = "model_results"
XGB_PARAMS = {
    'objective': 'reg:squarederror',
    'n_estimators': 100,
    'learning_rate': 0.05,
    'max_depth': 3,
    'subsample': 0.8,
    'colsample_bytree': 0.8,
    'random_state': 42,
    'n_jobs': -1
}


# --- UTILITY FUNCTIONS ---

def fit_nelson_siegel_to_panel(yield_panel, lambda_val):
    """
    Fits the Nelson-Siegel model to each row of a yield panel for a fixed lambda.
    Returns a time series of the level, slope, and curvature parameters.
    """
    print("\nFitting Nelson-Siegel model to the yield panel...")
    ns_params = []
    
    # --- FIX STARTS HERE ---
    # Original maturities might include 0, which causes division by zero.
    all_maturities = yield_panel.columns.astype(float)
    # Exclude zero maturity for NS calculation.
    maturities_for_ns = all_maturities[all_maturities > 0]
    
    if len(maturities_for_ns) < 3:
        raise ValueError("Nelson-Siegel fitting requires at least 3 non-zero maturities.")
    
    # Pre-calculate the factor loadings only for non-zero maturities
    lambda_tau = lambda_val * maturities_for_ns
    f1 = pd.Series(1.0, index=maturities_for_ns)
    f2 = (1 - np.exp(-lambda_tau)) / lambda_tau
    f3 = f2 - np.exp(-lambda_tau)
    
    X_ns = pd.DataFrame({'level': f1, 'slope': f2, 'curvature': f3})
    
    for date, yields in yield_panel.iterrows():
        # Select yields corresponding to the non-zero maturities
        y_ns = yields[maturities_for_ns].values
        
        # Check for NaNs in the slice of yields for this date
        if np.isnan(y_ns).any():
            # Skip this date if it has missing data for the required maturities
            continue
            
        ols_fit = sm.OLS(y_ns, X_ns).fit()
        ns_params.append({'date': date, 'level': ols_fit.params['level'], 
                          'slope': ols_fit.params['slope'], 'curvature': ols_fit.params['curvature']})
    # --- FIX ENDS HERE ---
        
    print("Nelson-Siegel fitting complete.")
    return pd.DataFrame(ns_params).set_index('date')


def create_features_xgboost(yield_data):
    """Creates features for the XGBoost model."""
    features = yield_data.copy()
    features.columns = [f"yield_mat_{col}" for col in features.columns]
    return features


# --- GENERIC FORECASTING WORKFLOW ---

def run_xgboost_expanding_window(y_full, x_full, h_steps, target_name):
    """A generic function to run an XGBoost expanding window forecast."""
    common_index = y_full.index.intersection(x_full.index)
    y_aligned, x_aligned = y_full.loc[common_index], x_full.loc[common_index]

    min_train_size = 24
    if len(y_aligned) < min_train_size + 1:
        print(f"    Target '{target_name}': Not enough data ({len(y_aligned)}) for expanding window.")
        return None, None

    y_preds_list, y_true_list, dates_list = [], [], []

    print(f"    Starting XGBoost expanding window forecast for '{target_name}'...")
    for i in range(min_train_size, len(y_aligned)):
        X_train, y_train = x_aligned.iloc[:i], y_aligned.iloc[:i]
        X_test = x_aligned.iloc[i:i + 1]
        
        model = xgb.XGBRegressor(**XGB_PARAMS)
        model.fit(X_train, y_train)
        
        y_pred_value = model.predict(X_test)[0]

        y_preds_list.append(y_pred_value)
        y_true_list.append(y_aligned.iloc[i])
        dates_list.append(y_aligned.index[i])

    y_true_eval, y_pred_eval = np.array(y_true_list), np.array(y_preds_list)
    
    mse = mean_squared_error(y_true_eval, y_pred_eval)
    oos_r2 = calculate_oos_r_squared(y_true_eval, y_pred_eval, 0)
    hit_ratio = calculate_hit_ratio(y_true_eval, y_pred_eval)
    
    errors_model = y_true_eval - y_pred_eval
    errors_rw = y_true_eval - 0
    dm_stat, p_value = diebold_mariano_test(errors_model, errors_rw, h=h_steps) if len(errors_model) > h_steps else (np.nan, np.nan)
    
    predictions_df = pd.DataFrame({
        'date': dates_list,
        'target': target_name,
        'horizon_months': h_steps,
        'actual_change': y_true_eval,
        'predicted_change': y_pred_eval
    })
    
    summary_dict = {
        'model': 'XGBoost', 'target': target_name, 'horizon_months': int(h_steps/2), # Convert back to months
        'MSE': mse, 'OOS_R2_vs_RW': oos_r2, 'HitRatio': hit_ratio,
        'DM_stat_vs_RW': dm_stat, 'DM_p_value_vs_RW': p_value
    }
    
    return predictions_df, summary_dict


# --- TASK-SPECIFIC RUNNERS ---

def run_task2_xgboost_key_rate(yield_panel_full, x_predictors):
    """Task 2: Predicts changes in key interest rates using XGBoost."""
    print("\n" + "="*50)
    print("### TASK 2: PREDICTING KEY-RATE CHANGES (Δyt) with XGBoost ###")
    print("="*50)

    all_predictions, results_summary = [], []
    for h_months in TASK2_HORIZONS_MONTHS:
        h_steps = int(np.ceil(h_months * 2))
        print(f"\nProcessing Horizon: {h_months} months ({h_steps} steps)")
        
        for mat_target in TASK2_TARGET_MATURITIES:
            if mat_target not in yield_panel_full.columns:
                print(f"Warning: Target maturity {mat_target} not in data, skipping.")
                continue
            
            y_full = (yield_panel_full[mat_target].shift(-h_steps) - yield_panel_full[mat_target]).dropna()
            preds_df, summary = run_xgboost_expanding_window(y_full, x_predictors, h_steps, f"Rate_Mat_{mat_target}")
            if preds_df is not None:
                all_predictions.append(preds_df)
                results_summary.append(summary)

    return pd.DataFrame(results_summary)


def run_task3_xgboost_ns_param(yield_panel_full, x_predictors):
    """Task 3: Predicts changes in Nelson-Siegel parameters using XGBoost."""
    print("\n" + "="*50)
    print("### TASK 3: PREDICTING NS PARAMETER CHANGES (Δa, Δb, Δc) with XGBoost ###")
    print("="*50)

    ns_params_df = fit_nelson_siegel_to_panel(yield_panel_full, lambda_val=NELSON_SIEGEL_LAMBDA)
    all_predictions, results_summary = [], []

    for h_months in TASK3_HORIZONS_MONTHS:
        h_steps = int(np.ceil(h_months * 2))
        print(f"\nProcessing Horizon: {h_months} months ({h_steps} steps)")

        for param_target in ['level', 'slope', 'curvature']:
            y_full = (ns_params_df[param_target].shift(-h_steps) - ns_params_df[param_target]).dropna()
            preds_df, summary = run_xgboost_expanding_window(y_full, x_predictors, h_steps, f"NS_{param_target}")
            if preds_df is not None:
                all_predictions.append(preds_df)
                results_summary.append(summary)
                
    return pd.DataFrame(results_summary)


# --- MAIN EXECUTION BLOCK ---

if __name__ == "__main__":
    create_dummy_data_if_not_exists(DATA_PATH_PATTERN, current_dir=".")
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    
    print("Loading and preparing initial data...")
    try:
        yield_panel = load_and_prepare_data_for_modeling(
            DATA_PATH_PATTERN,
            yield_is_percentage=YIELD_IS_PERCENTAGE,
            selected_maturities=None 
        )
        if yield_panel.empty:
            raise ValueError("Loaded data is empty.")
    except Exception as e:
        print(f"FATAL: Data loading failed: {e}")
        exit()
        
    xgboost_predictors = create_features_xgboost(yield_panel)
    
    task2_summary = run_task2_xgboost_key_rate(yield_panel, xgboost_predictors)
    task3_summary = run_task3_xgboost_ns_param(yield_panel, xgboost_predictors)
    
    print("\n\n" + "#"*60)
    print("### XGBoost - COMPREHENSIVE MODEL EVALUATION SUMMARY ###")
    print("#"*60)
    
    if not task2_summary.empty:
        print("\n--- Task 2: Key-Rate Change Prediction Summary (vs. Random Walk) ---")
        print(task2_summary.to_string())
        
    if not task3_summary.empty:
        print("\n--- Task 3: NS Parameter Change Prediction Summary (vs. Random Walk) ---")
        print(task3_summary.to_string())
        
    print("\nNOTE: OOS R^2 values are based on overlapping samples and may appear inflated.")
    print("Execution complete.")