import pandas as pd
import numpy as np
import os
import matplotlib.pyplot as plt

# 假设 utils.py 文件在同一目录下，或者将通用函数复制到此处
try:
    from utils import (load_and_prepare_data_for_modeling, calculate_oos_r_squared,
                       calculate_hit_ratio, diebold_mariano_test, create_dummy_data_if_not_exists,
                       CHINESE_FONT, DATA_PATH_PATTERN)
except ImportError:
    print("错误：无法导入utils.py。请确保该文件存在或将所需函数复制到此脚本中。")
    # 你可以在这里直接粘贴 utils.py 中的函数定义作为备用方案
    exit()

# --- 模型参数 ---
YIELD_IS_PERCENTAGE = True
# 固定期限利率的变动值：直接预测特定期限的利率在未来一段时间的变化量 (Δyt = yt - yt-h)
TARGET_MATURITIES_FOR_PREDICTION = [0.25, 1, 3, 5, 7, 10]  # 要预测的特定期限（年）
PREDICTION_HORIZON_MONTHS = [3, 6, 12]  # 预测未来h个月 (对应 Δyt = y_t+h - y_t)
# 注意：研报中 Δyt = yt - yt-h。这里为了方便，我们预测 y_t+h - y_t。符号相反，评估时注意。
# 或者，我们直接预测 y_t+h，然后计算变化。
# 为了与研报的 Δyt 一致，目标变量应该是 y_t - y_{t-h}
# 如果我们的数据是 y_t, y_{t+1}, ...
# 那么，在时间点 t，我们要预测的是 y_{t+h} - y_t

OUTPUT_DIR = "model_results"
BENCHMARK_RESULTS_FILE = os.path.join(OUTPUT_DIR, "benchmark_random_walk_predictions.csv")


def run_benchmark_model(yield_data, target_maturities, horizons_months):
    """
    运行随机游走基准模型。
    随机游走模型预测未来的变化值为零 (Δy_t+h = 0)，或者说 y_t+h = y_t。
    """
    print("\n--- 运行基准模型 (随机游走) ---")
    os.makedirs(OUTPUT_DIR, exist_ok=True)

    all_predictions_df = []
    results_summary = []

    for h_months in horizons_months:
        print(f"\n预测窗口: {h_months} 个月")
        # h_steps 对应于月度数据的步长
        h_steps = h_months

        # 准备目标变量 (Δyt+h = y_t+h - y_t)
        # 我们在 t 时刻，用 y_t 来预测 y_t+h
        # 真实的变化是 y_t+h - y_t
        # 随机游走预测 y_t+h_pred = y_t，因此预测的变化是 y_t+h_pred - y_t = 0

        for mat in target_maturities:
            if mat not in yield_data.columns:
                print(f"警告：期限 {mat} 不在数据中，跳过。")
                continue

            print(f"  处理期限: {mat} 年")
            series = yield_data[mat].copy()

            # y_t: 当前利率
            y_t = series
            # y_t_plus_h: 未来h期实际利率
            y_t_plus_h_actual = series.shift(-h_steps)

            # 真实的变化值 Δy_actual = y_t+h - y_t
            delta_y_actual = y_t_plus_h_actual - y_t

            # 随机游走预测的变化值 Δy_pred_rw = 0
            delta_y_pred_rw = pd.Series(0, index=y_t.index)

            # 清理NaN值 (由于shift操作和变化计算产生)
            valid_idx = delta_y_actual.notna()

            if not valid_idx.any():
                print(f"    期限 {mat}，窗口 {h_months} 个月：没有有效数据进行评估。")
                continue

            y_true_change = delta_y_actual[valid_idx]
            y_pred_rw_change = delta_y_pred_rw[valid_idx]  # 仍然是0

            # 评估指标
            mse_rw = mean_squared_error(y_true_change, y_pred_rw_change)  # 这实际上是 sum( (y_true_change - 0)^2 ) / N
            # OOS R^2 的基准是随机游走，所以随机游走模型的OOS R^2相对于自身应该是0 (如果计算正确)
            # 或者，如果用研报公式，分母是 sum(y_t^2)，则随机游走模型的分子也是 sum(y_t^2)，导致OOS R2=0
            oos_r2_rw = 1 - (mse_rw / np.mean(y_true_change ** 2)) if np.mean(y_true_change ** 2) != 0 else 0  # 应为0

            hit_ratio_rw = calculate_hit_ratio(y_true_change, y_pred_rw_change)

            print(f"    MSE (Random Walk): {mse_rw:.6g}")
            print(f"    OOS R^2 (Random Walk vs. Itself): {oos_r2_rw:.4f}")  # 理论上是0
            print(f"    Hit Ratio (Random Walk): {hit_ratio_rw:.4f}")

            # 保存预测结果 (这里保存的是预测的变化量，对于RW是0)
            # 为了与其他模型比较，我们应该保存 y_t_plus_h 的预测值
            # 对于随机游走，y_t_plus_h_pred = y_t
            predictions_current_run = pd.DataFrame({
                'date': y_t.index[valid_idx],
                'maturity': mat,
                'horizon_months': h_months,
                'y_t': y_t[valid_idx],
                'y_t_plus_h_actual': y_t_plus_h_actual[valid_idx],
                'delta_y_actual': y_true_change,
                'delta_y_pred_benchmark': y_pred_rw_change,  # RW预测的变化是0
                'y_t_plus_h_pred_benchmark': y_t[valid_idx]  # RW预测的未来值是当前值
            })
            all_predictions_df.append(predictions_current_run)

            results_summary.append({
                'model': 'RandomWalk',
                'maturity': mat,
                'horizon_months': h_months,
                'MSE': mse_rw,
                'OOS_R2_vs_ZeroChange': oos_r2_rw,  # 这个命名可能需要调整
                'HitRatio': hit_ratio_rw
            })

    if not all_predictions_df:
        print("错误：没有生成任何基准预测结果。")
        return pd.DataFrame(), pd.DataFrame()

    all_predictions_final_df = pd.concat(all_predictions_df, ignore_index=True)
    all_predictions_final_df.to_csv(BENCHMARK_RESULTS_FILE, index=False, encoding='utf-8-sig')
    print(f"\n基准模型预测结果已保存到: {BENCHMARK_RESULTS_FILE}")

    summary_df = pd.DataFrame(results_summary)
    print("\n基准模型评估摘要:")
    print(summary_df)

    return all_predictions_final_df, summary_df


if __name__ == "__main__":
    # 检查并创建虚拟数据
    create_dummy_data_if_not_exists(DATA_PATH_PATTERN, current_dir=".")

    # 加载数据
    try:
        yield_panel_monthly = load_and_prepare_data_for_modeling(
            DATA_PATH_PATTERN,
            yield_is_percentage=YIELD_IS_PERCENTAGE,
            selected_maturities=TARGET_MATURITIES_FOR_PREDICTION  # 只加载需要的期限
        )
    except Exception as e:
        print(f"数据加载失败: {e}")
        exit()

    if yield_panel_monthly.empty:
        print("加载的数据为空，无法继续。")
        exit()

    # 运行基准模型
    benchmark_predictions, benchmark_summary = run_benchmark_model(
        yield_panel_monthly,
        TARGET_MATURITIES_FOR_PREDICTION,
        PREDICTION_HORIZON_MONTHS
    )

    # 可选：绘制一些基准模型的实际值 vs 预测值 (对于变化量)
    if not benchmark_predictions.empty:
        sample_mat = TARGET_MATURITIES_FOR_PREDICTION[0]
        sample_hor = PREDICTION_HORIZON_MONTHS[0]
        plot_data = benchmark_predictions[
            (benchmark_predictions['maturity'] == sample_mat) &
            (benchmark_predictions['horizon_months'] == sample_hor)
            ]

        if not plot_data.empty:
            plt.figure(figsize=(12, 6))
            plt.plot(plot_data['date'], plot_data['delta_y_actual'], label=f'实际变化 (Δy_t+{sample_hor}m)', alpha=0.7)
            plt.plot(plot_data['date'], plot_data['delta_y_pred_benchmark'], label=f'随机游走预测变化 (总是0)',
                     linestyle='--')
            plt.title(f'基准模型: 期限 {sample_mat}年, 预测窗口 {sample_hor}月 - 利率变化')
            plt.xlabel('日期')
            plt.ylabel('利率变化 (Δy)')
            plt.legend()
            plt.grid(True)
            plt.tight_layout()
            plt.savefig(os.path.join(OUTPUT_DIR, f"benchmark_delta_y_plot_M{sample_mat}_H{sample_hor}.png"))
            plt.show()