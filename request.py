import requests
import pandas as pd
import time
from datetime import datetime, timedelta

# 生成从20110101到20201231的所有日期（格式：YYYY-MM-DD）
start_date = datetime.strptime("20070101", "%Y%m%d")
end_date = datetime.strptime("20201231", "%Y%m%d")
date_list = []
current = start_date
while current <= end_date:
    date_list.append(current.strftime("%Y-%m-%d"))
    current += timedelta(days=1)



url = "https://yield.chinabond.com.cn/cbweb-mn/yc/searchYc"
headers = {
    "Content-Type": "application/x-www-form-urlencoded",
    "User-Agent": "Mozilla/5.0"
}
payload_template = (
    "xyzSelect=txy&&workTimes={date}&&dxbj=0&&qxll=1,&&yqqxN=N&&yqqxK=K"
    "&&ycDefIds=8a8b2ca037a7ca910137bfaa94fa5057,&&wrjxCBFlag=0&&locale=zh_CN"
)




for year in range(start_date.year, end_date.year + 1):
    year_dates = [d for d in date_list if d.startswith(str(year))]
    results = []
    print(f"Processing year {year}, total days: {len(year_dates)}")
    for date in year_dates:
        payload = payload_template.format(date=date)
        try:
            resp = requests.post(url, data=payload, headers=headers, timeout=10)
            resp.raise_for_status()
            data = resp.json()
            if data and isinstance(data, list) and "seriesData" in data[0]:
                for item in data[0]["seriesData"]:
                    maturity, yield_value = item
                    results.append({
                        "date": date,
                        "maturity": maturity,
                        "yield": yield_value
                    })
            else:
                print(f"No data for {date}")
        except Exception as e:
            print(f"Error on {date}: {e}")
        time.sleep(1)  # 避免请求过快被封

    # 每年保存一次
    df = pd.DataFrame(results)
    outname = f"chinabond_yields_{year}.csv"
    df.to_csv(outname, index=False, encoding="utf-8-sig")
    print(f"Year {year} done. Data saved to {outname}")
