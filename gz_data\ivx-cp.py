import pandas as pd
import numpy as np
import os
import statsmodels.api as sm
from sklearn.metrics import mean_squared_error
from statsmodels.sandbox.regression.gmm import IV2SLS

# Assuming utils.py is in the same directory
from utils import (load_and_prepare_data_for_modeling, calculate_oos_r_squared,
                   calculate_hit_ratio, diebold_mariano_test, create_dummy_data_if_not_exists,
                   DATA_PATH_PATTERN)

# --- Task-Specific Parameters ---

# Parameters for Task 2: Key Rate Change Prediction
TASK2_TARGET_MATURITIES = [0.25, 1, 3, 5, 7, 10]
TASK2_HORIZONS_MONTHS = [12]

# Parameters for Task 3: Nelson-Siegel Parameter Prediction
TASK3_HORIZONS_MONTHS = [12]
# Fixed lambda for NS model, a common value from Diebold & Li (2006) for stability
NELSON_SIEGEL_LAMBDA = 0.0609

# --- General Parameters & File Paths ---
YIELD_IS_PERCENTAGE = True
OUTPUT_DIR = "model_results"
TASK2_RESULTS_FILE = os.path.join(OUTPUT_DIR, "key_rate_change_predictions.csv")
TASK3_RESULTS_FILE = os.path.join(OUTPUT_DIR, "ns_parameter_change_predictions.csv")


# --- UTILITY FUNCTIONS ---

def calculate_forward_rates(yield_data):
    """Calculates one-year forward rates from zero-coupon bond yields."""
    required_maturities = [1, 2, 3, 4, 5]
    if not all(mat in yield_data.columns for mat in required_maturities):
        raise ValueError("Forward rate calculation requires yields for maturities 1, 2, 3, 4, and 5.")

    yields_for_forwards = yield_data[required_maturities]
    forward_rates = pd.DataFrame(index=yields_for_forwards.index)

    if 1 in yields_for_forwards.columns:
        forward_rates['f_1'] = yields_for_forwards[1]

    for n in range(2, 6):
        if n in yields_for_forwards.columns and (n - 1) in yields_for_forwards.columns:
            p_n = -n * yields_for_forwards[n]
            p_n_minus_1 = -(n - 1) * yields_for_forwards[n - 1]
            forward_rates[f'f_{n}'] = p_n_minus_1 - p_n

    return forward_rates.dropna()


def ivx_regression(y, x):
    """Performs the IVX (Instrumental Variable eXtended) regression and returns coefficients."""
    common_index = y.index.intersection(x.index)
    y_aligned, x_aligned = y.loc[common_index], x.loc[common_index]

    if len(y_aligned) < 2:
        return sm.OLS(y, sm.add_constant(x)).fit().params.values

    try:
        exog_with_const = sm.add_constant(x_aligned)
        instruments = sm.add_constant(x_aligned.shift(1).bfill())
        iv_model = IV2SLS(endog=y_aligned, exog=exog_with_const, instrument=instruments).fit()
        return iv_model.params.values
    except Exception as e:
        print(f"IVX regression failed: {e}. Falling back to OLS.")
        return sm.OLS(y_aligned, sm.add_constant(x_aligned)).fit().params.values


def fit_nelson_siegel_to_panel(yield_panel, lambda_val):
    """
    Fits the Nelson-Siegel model to each row of a yield panel for a fixed lambda.
    Returns a time series of the level, slope, and curvature parameters.
    """
    print("\nFitting Nelson-Siegel model to the yield panel...")
    ns_params = []
    maturities = yield_panel.columns.astype(float)

    # Pre-calculate the factor loadings for speed
    lambda_tau = lambda_val * maturities
    f1 = pd.Series(1.0, index=maturities)
    f2 = (1 - np.exp(-lambda_tau)) / lambda_tau
    f3 = f2 - np.exp(-lambda_tau)

    X_ns = pd.DataFrame({'level': f1, 'slope': f2, 'curvature': f3})

    for date, yields in yield_panel.iterrows():
        # With fixed lambda, the NS parameters can be estimated with a simple OLS
        y_ns = yields.values
        ols_fit = sm.OLS(y_ns, X_ns).fit()
        # Parameters are level (a_t), slope (b_t), and curvature (c_t)
        ns_params.append({'date': date, 'level': ols_fit.params['level'],
                          'slope': ols_fit.params['slope'], 'curvature': ols_fit.params['curvature']})

    print("Nelson-Siegel fitting complete.")
    return pd.DataFrame(ns_params).set_index('date')


# --- TASK-SPECIFIC RUNNERS ---

def run_expanding_window_forecast(y_full, x_full, h_steps, model_name, target_name):
    """
    A generic function to run an expanding window forecast for a given target and predictors.
    """
    common_index = y_full.index.intersection(x_full.index)
    y_aligned, x_aligned = y_full.loc[common_index], x_full.loc[common_index]

    min_train_size = 24  # Using 2 years of semi-monthly data as minimum
    if len(y_aligned) < min_train_size + 1:
        print(f"    Target '{target_name}': Not enough data ({len(y_aligned)}) for expanding window.")
        return None, None

    y_preds_list, y_true_list, dates_list = [], [], []

    print(f"    Starting expanding window forecast for '{target_name}'...")
    for i in range(min_train_size, len(y_aligned)):
        X_train, y_train = x_aligned.iloc[:i], y_aligned.iloc[:i]
        X_test = x_aligned.iloc[i:i + 1]

        beta_hat = ivx_regression(y_train, X_train)
        X_test_with_const = sm.add_constant(X_test, has_constant='add')
        y_pred_value = (X_test_with_const.values @ beta_hat)[0]

        y_preds_list.append(y_pred_value)
        y_true_list.append(y_aligned.iloc[i])
        dates_list.append(y_aligned.index[i])

    y_true_eval, y_pred_eval = np.array(y_true_list), np.array(y_preds_list)

    # --- Evaluation ---
    # Note: As per user instructions, overlapping samples are used, which can inflate R-squared values.
    # These OOS R^2 values should not be directly compared with those from non-overlapping samples.
    mse = mean_squared_error(y_true_eval, y_pred_eval)
    oos_r2 = calculate_oos_r_squared(y_true_eval, y_pred_eval, 0)
    hit_ratio = calculate_hit_ratio(y_true_eval, y_pred_eval)

    errors_model = y_true_eval - y_pred_eval
    errors_rw = y_true_eval - 0
    dm_stat, p_value = diebold_mariano_test(errors_model, errors_rw, h=h_steps) if len(errors_model) > h_steps else (
    np.nan, np.nan)

    predictions_df = pd.DataFrame({
        'date': dates_list,
        'target': target_name,
        'horizon_months': h_steps,
        'actual_change': y_true_eval,
        'predicted_change': y_pred_eval
    })

    summary_dict = {
        'model': model_name, 'target': target_name, 'horizon_months': h_steps,
        'MSE': mse, 'OOS_R2_vs_RW': oos_r2, 'HitRatio': hit_ratio,
        'DM_stat_vs_RW': dm_stat, 'DM_p_value_vs_RW': p_value
    }

    return predictions_df, summary_dict


def run_task2_key_rate_prediction(yield_panel_full, x_predictors):
    """
    Task 2: Predicts changes in key interest rates using the IVX methodology.
    """
    print("\n" + "=" * 50)
    print("### TASK 2: PREDICTING KEY-RATE CHANGES (Δyt) ###")
    print("=" * 50)

    all_predictions = []
    results_summary = []

    for h_months in TASK2_HORIZONS_MONTHS:
        h_steps = h_months // 1.5 if YIELD_IS_PERCENTAGE else h_months  # Adjust for semi-monthly data if needed
        h_steps = int(np.ceil(h_months * 2))  # Approx 2 obs per month
        print(f"\nProcessing Horizon: {h_months} months ({h_steps} steps)")

        for mat_target in TASK2_TARGET_MATURITIES:
            if mat_target not in yield_panel_full.columns:
                print(f"Warning: Target maturity {mat_target} not in data, skipping.")
                continue

            y_full = yield_panel_full[mat_target].shift(-h_steps) - yield_panel_full[mat_target]
            y_full = y_full.dropna()

            preds_df, summary = run_expanding_window_forecast(y_full, x_predictors, h_steps, 'IVX',
                                                              f"Rate_Mat_{mat_target}")
            if preds_df is not None:
                all_predictions.append(preds_df)
                results_summary.append(summary)

    if not all_predictions:
        print("Task 2 Error: No predictions were generated.")
        return pd.DataFrame(), pd.DataFrame()

    final_df = pd.concat(all_predictions, ignore_index=True)
    final_df.to_csv(TASK2_RESULTS_FILE, index=False, encoding='utf-8-sig')
    print(f"\nTask 2 predictions saved to: {TASK2_RESULTS_FILE}")

    summary_df = pd.DataFrame(results_summary)
    return final_df, summary_df


def run_task3_ns_parameter_prediction(yield_panel_full, x_predictors):
    """
    Task 3: Predicts changes in Nelson-Siegel parameters using the IVX methodology.
    """
    print("\n" + "=" * 50)
    print("### TASK 3: PREDICTING NELSON-SIEGEL PARAMETER CHANGES (Δa, Δb, Δc) ###")
    print("=" * 50)

    # Step 1: Fit NS model to get parameter time series
    ns_params_df = fit_nelson_siegel_to_panel(yield_panel_full, lambda_val=NELSON_SIEGEL_LAMBDA)

    all_predictions = []
    results_summary = []

    for h_months in TASK3_HORIZONS_MONTHS:
        h_steps = int(np.ceil(h_months * 2))  # Approx 2 obs per month
        print(f"\nProcessing Horizon: {h_months} months ({h_steps} steps)")

        for param_target in ['level', 'slope', 'curvature']:
            y_full = ns_params_df[param_target].shift(-h_steps) - ns_params_df[param_target]
            y_full = y_full.dropna()

            preds_df, summary = run_expanding_window_forecast(y_full, x_predictors, h_steps, 'IVX',
                                                              f"NS_{param_target}")
            if preds_df is not None:
                all_predictions.append(preds_df)
                results_summary.append(summary)

    if not all_predictions:
        print("Task 3 Error: No predictions were generated.")
        return pd.DataFrame(), pd.DataFrame()

    final_df = pd.concat(all_predictions, ignore_index=True)
    final_df.to_csv(TASK3_RESULTS_FILE, index=False, encoding='utf-8-sig')
    print(f"\nTask 3 predictions saved to: {TASK3_RESULTS_FILE}")

    summary_df = pd.DataFrame(results_summary)
    return final_df, summary_df


# --- MAIN EXECUTION BLOCK ---

if __name__ == "__main__":
    create_dummy_data_if_not_exists(DATA_PATH_PATTERN, current_dir=".")
    os.makedirs(OUTPUT_DIR, exist_ok=True)

    print("Loading and preparing initial data...")
    try:
        # Load all maturities needed for features and all targets
        all_mats_needed = sorted(list(set([1, 2, 3, 4, 5] + TASK2_TARGET_MATURITIES)))
        yield_panel = load_and_prepare_data_for_modeling(
            DATA_PATH_PATTERN,
            yield_is_percentage=YIELD_IS_PERCENTAGE,
            selected_maturities=all_mats_needed
        )
        if yield_panel.empty:
            raise ValueError("Loaded data is empty.")
    except Exception as e:
        print(f"FATAL: Data loading failed: {e}")
        exit()

    # --- Create Common Predictors (Forward Rates) ---
    print("\nCreating predictor variables (forward rates)...")
    ivx_predictors = calculate_forward_rates(yield_panel)

    # --- Run Both Tasks ---
    task2_preds, task2_summary = run_task2_key_rate_prediction(yield_panel, ivx_predictors)
    task3_preds, task3_summary = run_task3_ns_parameter_prediction(yield_panel, ivx_predictors)

    # --- Final Combined Summary ---
    print("\n\n" + "#" * 60)
    print("### COMPREHENSIVE MODEL EVALUATION SUMMARY ###")
    print("#" * 60)

    if not task2_summary.empty:
        print("\n--- Task 2: Key-Rate Change Prediction Summary (vs. Random Walk) ---")
        print(task2_summary.to_string())

    if not task3_summary.empty:
        print("\n--- Task 3: NS Parameter Change Prediction Summary (vs. Random Walk) ---")
        print(task3_summary.to_string())

    print("\nNOTE: OOS R^2 values are based on overlapping samples and may appear inflated.")
    print("Execution complete.")