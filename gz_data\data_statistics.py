# -*- coding: utf-8 -*-
"""
数据统计信息查看脚本

本脚本用于读取国债期限结构数据，生成类似研报中的统计表格，
显示不同期限国债收益率的描述性统计信息（2007.01-2019.09）。
"""

import pandas as pd
from utils import load_and_prepare_data_for_modeling, DATA_PATH_PATTERN, YIELD_IS_PERCENTAGE

# --- 配置参数 ---
# 目标期限（年）
TARGET_MATURITIES = [0.25, 1, 3, 5, 7, 10]
# 数据截止日期（根据用户记忆，限制到2019年9月30日）
CUT_OFF_DATE = '2019-09-30'

def load_data_with_cutoff(path_pattern, cut_off_date=None, yield_is_percentage=True):
    """
    加载数据并应用截止日期过滤
    """
    print("正在加载数据...")

    # 使用utils中的函数加载数据
    yield_panel = load_and_prepare_data_for_modeling(
        path_pattern,
        yield_is_percentage=yield_is_percentage,
        selected_maturities=None  # 加载所有期限
    )

    if yield_panel.empty:
        raise ValueError("加载的数据为空")

    # 应用截止日期过滤
    if cut_off_date:
        try:
            cut_off_date_dt = pd.to_datetime(cut_off_date)
            original_rows = len(yield_panel)
            yield_panel = yield_panel[yield_panel.index <= cut_off_date_dt]
            filtered_rows = len(yield_panel)
            print(f"根据截止日期 {cut_off_date} 筛选，数据行数从 {original_rows} 减少到 {filtered_rows}")

            if yield_panel.empty:
                raise ValueError(f"截止日期 {cut_off_date} 之前没有数据")
        except Exception as e:
            print(f"警告：截止日期过滤失败: {e}")
            print("将使用所有可用数据")

    return yield_panel

def calculate_statistics(yield_panel, target_maturities):
    """
    计算描述性统计信息
    """
    # 筛选目标期限
    available_maturities = [m for m in target_maturities if m in yield_panel.columns]
    missing_maturities = [m for m in target_maturities if m not in yield_panel.columns]

    if missing_maturities:
        print(f"警告：以下期限在数据中缺失: {missing_maturities}")

    if not available_maturities:
        raise ValueError("所有目标期限均在数据中缺失")

    # 选择可用期限的数据
    data_subset = yield_panel[available_maturities]

    # 计算统计信息 - 直接使用pandas的describe方法，然后添加额外的统计量
    stats_df = data_subset.describe()

    # 重命名行索引为中文
    stats_df = stats_df.rename(index={
        'count': '观测数',
        'mean': '均值',
        'std': '标准差',
        'min': 'min',
        '25%': '25%',
        '50%': '50%',
        '75%': '75%',
        'max': 'max'
    })

    # 只保留我们需要的统计量
    desired_stats = ['均值', '标准差', 'min', '25%', '50%', '75%', 'max']
    stats_df = stats_df.loc[desired_stats]

    return stats_df

def format_statistics_table(stats_df):
    """
    格式化统计表格，使其类似研报中的样式
    """
    # stats_df现在的格式：行是统计指标，列是期限
    # 这正是我们想要的格式，不需要转置

    # 格式化数值（保留4位小数）
    formatted_df = stats_df.round(4)

    return formatted_df

def print_formatted_table(stats_df):
    """
    打印格式化的表格
    """
    print("\n" + "="*80)
    print("国债收益率描述性统计 (2007.01-2019.09)")
    print("="*80)

    # 创建表头
    maturities = stats_df.columns
    header = "国债期限".ljust(12)
    for mat in maturities:
        header += f"{float(mat):>12.2f}"
    print(header)
    print("-" * len(header))

    # 打印每一行
    for index, row in stats_df.iterrows():
        row_str = str(index).ljust(12)
        for value in row:
            row_str += f"{float(value):>12.4f}"
        print(row_str)

    print("="*80)

def save_statistics_to_csv(stats_df, filename="bond_yield_statistics.csv"):
    """
    保存统计结果到CSV文件
    """
    try:
        stats_df.to_csv(filename, encoding='utf-8-sig')
        print(f"\n统计结果已保存到: {filename}")
    except Exception as e:
        print(f"保存文件时出错: {e}")

def main():
    """
    主函数
    """
    try:
        # 加载数据
        yield_panel = load_data_with_cutoff(
            DATA_PATH_PATTERN,
            cut_off_date=CUT_OFF_DATE,
            yield_is_percentage=YIELD_IS_PERCENTAGE
        )

        print(f"数据时间范围: {yield_panel.index.min()} 到 {yield_panel.index.max()}")
        print(f"数据形状: {yield_panel.shape}")
        print(f"可用期限: {sorted(yield_panel.columns.tolist())}")

        # 计算统计信息
        stats_df = calculate_statistics(yield_panel, TARGET_MATURITIES)

        # 格式化表格
        formatted_stats = format_statistics_table(stats_df)

        # 可选：显示详细的统计信息
        # print("详细统计信息:")
        # print(stats_df)

        # 打印表格
        print_formatted_table(formatted_stats)

        # 保存到CSV
        save_statistics_to_csv(formatted_stats)

        # 额外信息
        print(f"\n数据观测数量: {len(yield_panel)}")
        print(f"分析期限数量: {len(formatted_stats.columns)}")

    except Exception as e:
        print(f"执行过程中出错: {e}")
        return

if __name__ == "__main__":
    main()
