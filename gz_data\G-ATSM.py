import pandas as pd
import numpy as np
from sklearn.decomposition import PCA
from sklearn.linear_model import LinearRegression
import matplotlib.pyplot as plt
import glob
import os
import statsmodels.api as sm # For OLS with stats

# --- Configuration ---
K_FACTORS = 3
# Use a common short rate proxy, e.g., 3-month (0.25 years) or 1-month (0.0833 years)
# The choice depends on data availability and liquidity. Let's try 3-month.
SHORT_RATE_MATURITY_YEARS = 1
DATA_PATH_PATTERN = "gz_spot_select_*.csv" # Make sure your CSVs are in the same directory or provide full path pattern
DT = 1/12 # Time step in years (monthly data)
TARGET_MATURITIES = [0.25, 0.5, 0.75, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10] # 目标久期，用于分析不同久期国债的差异
YIELD_IS_PERCENTAGE = True  # 收益率是否以百分比表示
# 使用中文显示
CHINESE_FONT = 'SimHei'

# --- 1. Load and Prepare Data ---
def load_and_prepare_data(path_pattern, date_col='date', maturity_col='maturity', yield_col='yield'):
    """
    加载多个CSV文件，合并，并将长格式数据转换为宽格式。
    参数:
    path_pattern (str): 匹配CSV文件名的模式。
    date_col (str): 日期列名。
    maturity_col (str): 期限列名。
    yield_col (str): 收益率列名。
    
    返回:
    pandas.DataFrame: 预处理后的宽格式数据，行为日期，列为期限。
    """
    all_files = glob.glob(path_pattern)
    all_files.sort()  # 确保按文件名（通常包含年份）顺序读取
    
    if not all_files:
        raise FileNotFoundError(f"未找到匹配模式的文件: {path_pattern}")
    
    df_list = []
    print("信息：正在读取以下文件:")
    for filename in all_files:
        print(f" - {filename}")
        try:
            df_temp = pd.read_csv(filename)
            df_list.append(df_temp)
        except Exception as e:
            print(f"读取文件 {filename} 时出错: {e}")
            continue  # 跳过有问题的文件
    
    if not df_list:
        raise ValueError("没有加载任何数据。请检查CSV文件和模式。")

    full_df = pd.concat(df_list, ignore_index=True)
    print(f"信息：已合并 {len(df_list)} 个文件，共 {len(full_df)} 行原始数据。")

    # 转换日期列为日期时间对象
    try:
        full_df[date_col] = pd.to_datetime(full_df[date_col])
    except Exception as e:
        print(f"错误：转换 '{date_col}' 列为日期格式时出错: {e}")
        return pd.DataFrame()

    # 如果收益率是百分比形式（例如2.5%表示为2.5），则转换为小数
    if YIELD_IS_PERCENTAGE:
        print("信息：收益率以百分比形式表示，正在转换为小数。")
        full_df[yield_col] = full_df[yield_col] / 100.0

    # 按日期和期限排序
    full_df = full_df.sort_values(by=[date_col, maturity_col])

    # 数据透视：将长格式转换为宽格式，日期为索引，期限为列
    yield_panel = full_df.pivot_table(index=date_col, columns=maturity_col, values=yield_col)
    
    # 重采样为月末数据（这类模型通常使用）
    yield_panel_monthly = yield_panel.resample('M').last()  # 使用每月最后一个观测值
    
    # 处理缺失值
    # 1. 前向填充（传播最后一个有效观测值）
    yield_panel_monthly = yield_panel_monthly.ffill()
    # 2. 后向填充（处理开始部分的NaN）
    yield_panel_monthly = yield_panel_monthly.bfill()
    # 3. 删除仍然有NaN的行（如果任何期限在所有时期都是NaN）
    yield_panel_monthly = yield_panel_monthly.dropna(axis=0, how='any')
    # 4. 删除在ffill/bfill后仍全为NaN的列（如果有）
    yield_panel_monthly = yield_panel_monthly.dropna(axis=1, how='all')
    
    print(f"信息：月度收益率面板形状: {yield_panel_monthly.shape}")
    if yield_panel_monthly.empty:
        raise ValueError("处理后的收益率面板为空。请检查数据和重采样过程。")
    
    # 筛选目标期限，确保存在于数据中
    available_maturities = [m for m in TARGET_MATURITIES if m in yield_panel_monthly.columns]
    missing_maturities = [m for m in TARGET_MATURITIES if m not in yield_panel_monthly.columns]
    
    if missing_maturities:
        print(f"警告：以下目标期限在数据中缺失: {missing_maturities}")
    
    if not available_maturities:
        print(f"警告：所有目标期限均不在数据中。将使用所有可用期限。")
    else:
        yield_panel_monthly = yield_panel_monthly[available_maturities]
        print(f"信息：已筛选出以下期限进行分析: {available_maturities}")

    # 确保SHORT_RATE_MATURITY_YEARS在列中
    if SHORT_RATE_MATURITY_YEARS not in yield_panel_monthly.columns:
        # 如果找不到精确匹配，则使用最接近的期限
        available_maturities = np.array(yield_panel_monthly.columns)
        closest_maturity = available_maturities[np.abs(available_maturities - SHORT_RATE_MATURITY_YEARS).argmin()]
        print(f"警告：未找到精确短期利率期限 {SHORT_RATE_MATURITY_YEARS}。使用最接近的: {closest_maturity}")
        global SHORT_RATE_MATURITY_YEARS_ACTUAL
        SHORT_RATE_MATURITY_YEARS_ACTUAL = closest_maturity
    else:
        SHORT_RATE_MATURITY_YEARS_ACTUAL = SHORT_RATE_MATURITY_YEARS
    
    return yield_panel_monthly

# --- 2. PCA for State Variables ---
def get_state_variables(yield_df, k_factors):
    """
    对处理好的收益率数据进行PCA分析，获取状态变量。
    
    参数:
    yield_df (pandas.DataFrame): 宽格式的收益率数据。
    k_factors (int): 要提取的主成分数量。
    
    返回:
    pandas.DataFrame: 包含主成分（状态变量）的数据框。
    """
    if yield_df.empty:
        print("错误：传入的收益率数据为空，无法进行PCA分析。")
        return pd.DataFrame()
    
    print("\n--- 开始执行主成分分析 (PCA) ---")
    
    pca = PCA(n_components=k_factors)
    # 在PCA前对数据进行标准化，以获得更好的结果
    yield_standardized = yield_df.copy()
    # yield_standardized = (yield_df - yield_df.mean()) / yield_df.std()
    # 删除标准化后全为NaN的列（如果标准差为0）
    yield_standardized = yield_standardized.dropna(axis=1, how='all')
    
    if yield_standardized.shape[1] < k_factors:
        raise ValueError(f"可用期限数量 ({yield_standardized.shape[1]}) 小于k_factors ({k_factors})。请减少k_factors。")
    
    X_t = pca.fit_transform(yield_standardized)
    X_t_df = pd.DataFrame(X_t, index=yield_df.index, columns=[f'主成分{i+1}' for i in range(k_factors)])
    
    # 分析解释方差
    explained_variance_ratio = pca.explained_variance_ratio_
    print(f"信息：各主成分解释的方差比例: {explained_variance_ratio}")
    print(f"信息：前{k_factors}个主成分累计解释的方差比例: {np.sum(explained_variance_ratio):.4f}")
    
    # 获取主成分载荷（系数）
    loadings = pca.components_

    # 检查第一主成分的载荷符号，如果大多数为负，则翻转
    if np.sum(loadings[0] < 0) > np.sum(loadings[0] > 0):  # 如果负载荷数量多于正载荷
        print("信息：第一主成分载荷大多为负，正在翻转第一主成分符号...")
        loadings[0] = -loadings[0]  # 翻转第一主成分载荷
        X_t[:, 0] = -X_t[:, 0]  # 同时翻转对应的主成分时间序列
    
    loadings_df = pd.DataFrame(loadings.T,
                            columns=[f'主成分{i+1}' for i in range(k_factors)],
                            index=yield_df.columns)
    print("\n信息：主成分载荷 (系数):")
    print(loadings_df)
    
    # 绘制主成分载荷与期限的关系图
    try:
        plt.rcParams['font.sans-serif'] = [CHINESE_FONT]
        plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题
    except Exception as e:
        print(f"警告：设置中文字体 '{CHINESE_FONT}' 失败: {e}")
        print("图表中的中文可能无法正常显示。请确保已安装该字体或更换为其他可用字体。")
    
    # 主成分载荷图
    plt.figure(figsize=(14, 7))
    
    # 确保载荷的横坐标是数值类型以便正确绘图和排序
    plot_loadings_df = loadings_df.copy()
    try:
        plot_loadings_df.index = plot_loadings_df.index.astype(float)
        plot_loadings_df = plot_loadings_df.sort_index()
        x_ticks_values = plot_loadings_df.index
    except ValueError:
        print("警告：期限（载荷图的X轴）无法转换为数值类型，将使用原始索引绘图。")
        x_ticks_values = yield_df.columns  # 使用原始列名作为刻度
    
    for col in plot_loadings_df.columns:
        plt.plot(plot_loadings_df.index, plot_loadings_df[col], marker='o', linestyle='-', label=col)
    
    plt.title('主成分载荷与期限的关系', fontsize=16)
    plt.xlabel('期限 (年)', fontsize=12)
    plt.ylabel('载荷 (系数值)', fontsize=12)
    if isinstance(x_ticks_values, pd.Index) and pd.api.types.is_numeric_dtype(x_ticks_values):
        plt.xticks(ticks=x_ticks_values, labels=[str(round(val,2)) for val in x_ticks_values], rotation=45, ha="right")
    else:
        plt.xticks(rotation=45, ha="right")
    
    plt.axhline(0, color='black', linewidth=0.8, linestyle='--')
    plt.legend(fontsize=10)
    plt.grid(True, linestyle='--', alpha=0.7)
    plt.tight_layout()
    plt.savefig('g_atsm_principal_components_loadings.png')
    plt.show()
    print("信息：主成分载荷图已显示并保存为 'g_atsm_principal_components_loadings.png'")
    
    # 主成分时间序列图
    plt.figure(figsize=(14, 7))
    for col in X_t_df.columns:
        plt.plot(X_t_df.index, X_t_df[col], label=col)
    plt.title('主成分历史走势', fontsize=16)
    plt.xlabel('日期', fontsize=12)
    plt.ylabel('主成分值', fontsize=12)
    plt.legend(fontsize=10)
    plt.grid(True, linestyle='--', alpha=0.7)
    plt.tight_layout()
    plt.savefig('g_atsm_principal_components_timeseries.png')
    plt.show()
    print("信息：主成分时间序列图已显示并保存为 'g_atsm_principal_components_timeseries.png'")
    
    return X_t_df

# --- 3. Estimate G-ATSM Parameters (ACM 2013) ---
def estimate_atsm_parameters(X_t_df, yields_df, short_rate_maturity_actual, k_factors, dt):
    X = X_t_df.values
    T = X.shape[0] # Number of time periods

    # OLS 1: VAR(1) for X_t: X_{t+1} = mu + Phi * X_t + v_{t+1}
    X_lagged = X[:-1, :]
    X_current = X[1:, :]
    
    X_lagged_with_const = sm.add_constant(X_lagged) # Add constant for mu
    
    # phi_coeffs will be (K+1) x K. First row is mu', subsequent rows form Phi'
    phi_coeffs_lst = []
    v_residuals_lst = []

    for k in range(k_factors):
        model_var = sm.OLS(X_current[:, k], X_lagged_with_const)
        results_var = model_var.fit()
        phi_coeffs_lst.append(results_var.params)
        v_residuals_lst.append(results_var.resid)

    phi_coeffs_stacked = np.array(phi_coeffs_lst).T # (K+1) x K
    mu_p = phi_coeffs_stacked[0, :].reshape(k_factors, 1)      # Kx1
    Phi_p = phi_coeffs_stacked[1:, :]                         # KxK
    
    v_residuals = np.array(v_residuals_lst).T # (T-1) x K
    Sigma = np.cov(v_residuals, rowvar=False) # KxK

    # OLS 2: Short rate r_t = delta0 + delta1' * X_t
    # Use yields aligned with X_lagged for r_t, to match timing for lambda estimation.
    # Or, use yields aligned with X for r_t, and X_lagged for lambda estimation.
    # ACM (2013) p.10 shows r_t, so it's X_t (not X_lagged).
    # We need to use all X_t for this regression, not just X_t[:-1]
    short_rate_annual = yields_df[short_rate_maturity_actual].values
    
    # Ensure short_rate_annual aligns with X (which is X_t_df.values)
    if len(short_rate_annual) != T:
        # This can happen if yields_df had a different index length than X_t_df after PCA/dropna
        # A robust way is to use the index of X_t_df
        short_rate_annual = yields_df.loc[X_t_df.index, short_rate_maturity_actual].values

    short_rate_period = short_rate_annual * dt # Convert annualized to period rate

    X_with_const_sr = sm.add_constant(X)
    model_sr = sm.OLS(short_rate_period, X_with_const_sr)
    results_sr = model_sr.fit()
    delta0_period = results_sr.params[0]         # scalar
    delta1_period = results_sr.params[1:].reshape(k_factors, 1) # Kx1

    # OLS 3: Market Prices of Risk: v_{t+1} = -Sigma*lambda0 - Sigma*lambda1*X_t + e_{t+1}
    # Regress v_residuals (shape (T-1)xK) on X_lagged (shape (T-1)xK)
    # X_lagged is X_t, v_residuals is v_{t+1}
    
    # Coefficients for lambda (b0_neg_Sigma_lambda0, b1_neg_Sigma_lambda1)
    # b_coeffs will be (K+1) x K. First row is (-Sigma*lambda0)', subsequent rows form (-Sigma*lambda1)'
    b_coeffs_lst = []
    for k in range(k_factors): # Regress each component of v_residuals
        model_lambda = sm.OLS(v_residuals[:, k], X_lagged_with_const) # X_lagged_with_const is X_t with const
        results_lambda = model_lambda.fit()
        b_coeffs_lst.append(results_lambda.params)
    
    b_coeffs_stacked = np.array(b_coeffs_lst).T # (K+1) x K
    
    neg_Sigma_lambda0_T = b_coeffs_stacked[0, :]      # 1xK (transpose of -Sigma*lambda0)
    neg_Sigma_lambda1_T = b_coeffs_stacked[1:, :]     # KxK (transpose of -Sigma*lambda1)

    Sigma_inv = np.linalg.inv(Sigma)
    
    lambda0 = -Sigma_inv @ neg_Sigma_lambda0_T.reshape(k_factors, 1) # Kx1
    lambda1 = -Sigma_inv @ neg_Sigma_lambda1_T.T                     # KxK ( (Sigma_lambda1_T)' = lambda1'Sigma )

    return mu_p, Phi_p, Sigma, delta0_period, delta1_period, lambda0, lambda1, Sigma_inv

# --- 4. Calculate No-Arbitrage Yields ---
def calculate_no_arbitrage_yields(X_t_df, model_maturities_years, 
                                  mu_p, Phi_p, Sigma, 
                                  delta0_period, delta1_period, 
                                  lambda0, lambda1, dt, k_factors):
    X = X_t_df.values
    T = X.shape[0]

    # Q-measure parameters
    mu_q = mu_p - Sigma @ lambda0    # Kx1
    Phi_q = Phi_p - Sigma @ lambda1  # KxK

    max_maturity_periods = int(np.max(model_maturities_years) / dt)
    
    A_coeffs = np.zeros(max_maturity_periods + 1)
    B_coeffs = np.zeros((max_maturity_periods + 1, k_factors)) # (max_n+1) x K

    # Recursions for A_n, B_n (for period rate)
    # A_0 = 0, B_0 = 0 (already initialized)
    for n in range(max_maturity_periods): # Iterate from n=0 to max_n-1 to get A_1,B_1 ... A_max_n, B_max_n
        A_coeffs[n+1] = A_coeffs[n] + B_coeffs[n,:] @ mu_q + delta0_period - 0.5 * B_coeffs[n,:] @ Sigma @ B_coeffs[n,:].T
        B_coeffs[n+1,:] = B_coeffs[n,:] @ Phi_q + delta1_period.T
        # Ensure B_coeffs[n+1,:] remains 1xK
        if B_coeffs[n+1,:].ndim > 1 and B_coeffs[n+1,:].shape[0] > 1 : B_coeffs[n+1,:] = B_coeffs[n+1,:].flatten()


    # Calculate model yields for required maturities
    model_yields_panel = pd.DataFrame(index=X_t_df.index)

    for mat_yr in model_maturities_years:
        n_periods = int(round(mat_yr / dt)) # Number of periods for this maturity
        if n_periods == 0: # Handle zero maturity if present, though typically not used in A,B recursion
            if mat_yr == SHORT_RATE_MATURITY_YEARS_ACTUAL: # If it's the short rate
                 # yields_period = delta0_period + X @ delta1_period -> this is 1-period yield
                 # for zero maturity, this is not well-defined by A,B.
                 # Could use the input short rate directly or skip plotting MAE for 0.
                 # For now, we will use the 1-period model rate for the shortest maturity in plot.
                 # The formula y(n) = -A_n/n - B_n'X_t/n is problematic for n=0.
                 # Let's assume the shortest maturity for plotting is 1 period (e.g., 1 month)
                if n_periods == 0 and mat_yr > 0 : # if mat_yr = 0.01, n_periods might be 0
                    n_periods = 1 # Smallest non-zero period
                elif n_periods == 0 and mat_yr == 0:
                    # MAE for 0 maturity is ill-defined with this formula, skip
                    print(f"Skipping MAE calculation for maturity 0 as it's ill-defined by A_n/n.")
                    continue


        if n_periods > max_maturity_periods:
            print(f"Warning: Maturity {mat_yr}yr ({n_periods} periods) exceeds max calculated {max_maturity_periods} periods. Skipping.")
            continue
        if n_periods <= 0: # Should not happen if mat_yr > 0 and dt > 0
            print(f"Warning: Maturity {mat_yr}yr gives non-positive {n_periods} periods. Skipping.")
            continue

        A_n = A_coeffs[n_periods]
        B_n = B_coeffs[n_periods, :].reshape(k_factors, 1) # Kx1

        # model_yield_period_rate = - (A_n / n_periods) - (X @ B_n / n_periods).flatten()
        # X is T x K, B_n is K x 1. X @ B_n is T x 1.
        term_A = -A_n / n_periods
        term_B = -(X @ B_n).flatten() / n_periods
        model_yield_period_rate = term_A + term_B
        
        model_yield_annualized = model_yield_period_rate / dt # Annualize
        model_yields_panel[mat_yr] = model_yield_annualized
        
    return model_yields_panel

# --- 5. 分析不同久期国债收益率差异 ---
def analyze_yield_differences(yield_panel, model_yields=None):
    """
    分析不同久期国债收益率的差异，计算各种利差指标。
    
    参数:
    yield_panel (pandas.DataFrame): 实际收益率面板数据，行为日期，列为期限。
    model_yields (pandas.DataFrame): 模型估计的收益率，如果提供，则同时分析模型与实际值的差异。
    """
    if yield_panel.empty:
        print("错误：收益率面板为空，无法分析久期差异。")
        return
    
    print("\n--- 分析不同久期国债收益率差异 ---")
    
    # 获取所有期限并排序
    maturities = sorted(yield_panel.columns)
    
    # 1. 计算基本统计量
    print("\n1. 各期限收益率基本统计量:")
    stats = yield_panel.describe().T
    stats['volatility'] = yield_panel.std()
    print(stats[['count', 'mean', 'std', 'min', 'max']])
    
    # 2. 计算重要的期限利差（典型的期限利差）
    print("\n2. 重要的期限利差:")
    
    # 定义要分析的利差
    spreads_to_analyze = []
    
    # 自动添加常见利差
    if 10 in maturities and 2 in maturities:
        spreads_to_analyze.append((10, 2, "10年-2年"))
    if 10 in maturities and 3 in maturities:
        spreads_to_analyze.append((10, 3, "10年-3年"))
    if 5 in maturities and 2 in maturities:
        spreads_to_analyze.append((5, 2, "5年-2年"))
    if 5 in maturities and 3 in maturities:
        spreads_to_analyze.append((5, 3, "5年-3年"))
    if 10 in maturities and 5 in maturities:
        spreads_to_analyze.append((10, 5, "10年-5年"))
    if 3 in maturities and 1 in maturities:
        spreads_to_analyze.append((3, 1, "3年-1年"))
    if 1 in maturities and 0.25 in maturities:
        spreads_to_analyze.append((1, 0.25, "1年-3月"))
    
    # 添加任意短期和长期利差
    longest_mat = max(maturities)
    shortest_mat = min(maturities)
    spreads_to_analyze.append((longest_mat, shortest_mat, f"{longest_mat}年-{shortest_mat}年"))
    
    # 计算并显示各利差的统计信息
    for long_mat, short_mat, name in spreads_to_analyze:
        if long_mat in yield_panel.columns and short_mat in yield_panel.columns:
            spread = yield_panel[long_mat] - yield_panel[short_mat]
            print(f"{name}利差:")
            print(f"  平均: {spread.mean():.4f}, 标准差: {spread.std():.4f}, 最小: {spread.min():.4f}, 最大: {spread.max():.4f}")
            
            # 如果提供了模型收益率，同时计算模型利差
            if model_yields is not None and long_mat in model_yields.columns and short_mat in model_yields.columns:
                model_spread = model_yields[long_mat] - model_yields[short_mat]
                mae = np.mean(np.abs(spread - model_spread))
                print(f"  模型拟合MAE: {mae*10000:.2f}基点")
    
    # 3. 绘制主要利差的时间序列图
    plt.figure(figsize=(14, 7))
    linestyles = ['-', '--', '-.', ':']
    for i, (long_mat, short_mat, name) in enumerate(spreads_to_analyze[:4]):  # 限制绘制前4个利差
        if long_mat in yield_panel.columns and short_mat in yield_panel.columns:
            spread = yield_panel[long_mat] - yield_panel[short_mat]
            plt.plot(spread.index, spread, label=name, linestyle=linestyles[i % len(linestyles)])
    
    plt.title('主要期限利差历史走势', fontsize=16)
    plt.xlabel('日期', fontsize=12)
    plt.ylabel('利差（百分点）', fontsize=12)
    plt.legend(fontsize=10)
    plt.grid(True, linestyle='--', alpha=0.7)
    plt.tight_layout()
    plt.savefig('yield_spreads_timeseries.png')
    plt.show()
    print("信息：期限利差时间序列图已显示并保存为 'yield_spreads_timeseries.png'")
    
    # 4. 绘制收益率曲线在不同时期的形状
    # 选取几个代表性时间点：开始、结束、中间
    dates_to_plot = [yield_panel.index[0], yield_panel.index[len(yield_panel) // 2], yield_panel.index[-1]]
    dates_labels = [d.strftime('%Y-%m-%d') for d in dates_to_plot]
    
    plt.figure(figsize=(14, 7))
    for i, date in enumerate(dates_to_plot):
        plt.plot(yield_panel.columns, yield_panel.loc[date], marker='o', linestyle=linestyles[i % len(linestyles)], label=dates_labels[i])
        
        # 如果提供了模型收益率，添加模型拟合曲线
        if model_yields is not None and date in model_yields.index:
            plt.plot(model_yields.columns, model_yields.loc[date], marker='x', linestyle=linestyles[i % len(linestyles)], alpha=0.6, label=f"{dates_labels[i]} (模型)")
    
    plt.title('不同时期的收益率曲线形状', fontsize=16)
    plt.xlabel('期限（年）', fontsize=12)
    plt.ylabel('收益率', fontsize=12)
    plt.legend(fontsize=10)
    plt.grid(True, linestyle='--', alpha=0.7)
    plt.tight_layout()
    plt.savefig('yield_curves_selected_dates.png')
    plt.show()
    print("信息：不同时期的收益率曲线图已显示并保存为 'yield_curves_selected_dates.png'")

# --- 6. Main Execution ---
def main():
    try:
        print("1. 加载和准备数据...")
        yield_panel_monthly = load_and_prepare_data(DATA_PATH_PATTERN)
        
        if yield_panel_monthly.empty:
            print("没有数据可处理。退出程序。")
            return

        print("\n2. 执行PCA提取状态变量...")
        X_t_df = get_state_variables(yield_panel_monthly, K_FACTORS)

        print("\n3. 估计G-ATSM参数...")
        mu_p, Phi_p, Sigma, delta0_period, delta1_period, lambda0, lambda1, Sigma_inv = \
            estimate_atsm_parameters(X_t_df, yield_panel_monthly, SHORT_RATE_MATURITY_YEARS_ACTUAL, K_FACTORS, DT)

        print("\n--- 估计的参数 ---")
        print(f"μ_p (P测度漂移项):\n{mu_p}")
        print(f"Φ_p (P测度转移矩阵):\n{Phi_p}")
        print(f"Σ (P测度协方差矩阵):\n{Sigma}")
        print(f"δ₀ (短期利率截距项，阶段利率): {delta0_period:.6f}")
        print(f"δ₁ (短期利率因子敏感度，阶段利率):\n{delta1_period}")
        print(f"λ₀ (风险价格截距):\n{lambda0}")
        print(f"λ₁ (风险价格因子敏感度):\n{lambda1}")
        
        print("\n4. 计算无套利收益率...")
        # 使用原始数据中的期限计算模型收益率
        original_maturities_years = sorted(yield_panel_monthly.columns.tolist())
        
        # 移除0期限（如果存在），因为A_n/n计算中会有问题，需要单独处理
        plot_maturities_years = [m for m in original_maturities_years if m > 0]

        model_yields_panel = calculate_no_arbitrage_yields(X_t_df, plot_maturities_years,
                                                        mu_p, Phi_p, Sigma,
                                                        delta0_period, delta1_period,
                                                        lambda0, lambda1, DT, K_FACTORS)

        # 5. 分析不同久期国债收益率差异
        print("\n5. 分析不同久期国债收益率差异...")
        analyze_yield_differences(yield_panel_monthly, model_yields_panel)

        print("\n6. 计算MAE...")
        maes_bps = []
        valid_plot_maturities = []

        # 确保观测收益率与模型收益率日期对齐（X_t_df.index）
        observed_yields_aligned = yield_panel_monthly.loc[X_t_df.index, :]

        for mat_yr in plot_maturities_years:
            if mat_yr in model_yields_panel.columns and mat_yr in observed_yields_aligned.columns:
                obs_yields = observed_yields_aligned[mat_yr].values
                mod_yields = model_yields_panel[mat_yr].values
                
                # 确保没有NaN值
                valid_idx = ~np.isnan(obs_yields) & ~np.isnan(mod_yields)
                if np.sum(valid_idx) == 0:
                    print(f"警告：期限 {mat_yr} 没有有效的重叠数据。跳过MAE计算。")
                    continue

                mae = np.mean(np.abs(obs_yields[valid_idx] - mod_yields[valid_idx]))
                maes_bps.append(mae * 10000) # 转换为基点
                valid_plot_maturities.append(mat_yr)
            else:
                print(f"期限 {mat_yr} 在模型收益率或观测收益率中未找到。跳过MAE计算。")

        if not maes_bps:
            print("没有计算任何MAE。无法绘图。")
            return

        print("\n--- MAE (基点) ---")
        for mat, mae_val in zip(valid_plot_maturities, maes_bps):
            print(f"期限 {mat:.2f} 年: {mae_val:.2f} 基点")

        print("\n7. 绘制MAE图...")
        plt.figure(figsize=(12, 7))
        plt.bar(valid_plot_maturities, maes_bps, width=0.2, color='skyblue') # 根据需要调整宽度
        
        for i, val in enumerate(maes_bps):
             plt.text(valid_plot_maturities[i], val + (max(maes_bps)*0.01), f"{val:.1f}", ha='center', va='bottom', fontsize=8)

        plt.xlabel("期限（年）")
        plt.ylabel("平均绝对误差（基点）")
        plt.title(f"G-ATSM：无套利收益率 vs. 观测收益率 MAE (K={K_FACTORS} 因子)")
        plt.xticks(valid_plot_maturities, rotation=45)
        plt.grid(axis='y', linestyle='--')
        plt.tight_layout()
        plt.savefig("g_atsm_mae_plot.png")
        plt.show()

    except FileNotFoundError as e:
        print(f"错误：{e}。请确保数据文件可访问。")
    except ValueError as e:
        print(f"值错误：{e}。请检查数据完整性或模型参数。")
    except np.linalg.LinAlgError as e:
        print(f"线性代数错误：{e}。这可能是由于奇异矩阵（如Σ）导致的。请检查数据或PCA。")
    except Exception as e:
        import traceback
        print(f"发生意外错误：{e}")
        traceback.print_exc()

if __name__ == "__main__":
    # 设置matplotlib中文字体
    try:
        plt.rcParams['font.sans-serif'] = [CHINESE_FONT]
        plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题
    except Exception as e:
        print(f"警告：设置中文字体 '{CHINESE_FONT}' 失败: {e}")
    
    # 检查是否存在数据文件，如果不存在，创建测试数据
    current_dir = os.path.dirname(os.path.abspath(__file__)) # 获取脚本所在目录
    
    # 检查是否存在任何数据文件，如果不存在，创建测试数据
    if not glob.glob(os.path.join(current_dir, "gz_spot_select_*.csv")):
        print("未找到数据文件。正在创建测试数据用于演示...")
        dates = pd.to_datetime(['2007-01-04', '2007-01-05', '2007-01-08', 
                              '2007-02-01', '2007-02-02', '2007-02-05',
                              '2008-03-01', '2008-03-02', '2008-03-05'])
        maturities = [0, 0.25, 0.5, 0.75, 1, 1.5, 2, 2.5, 3, 5, 7, 10]
        
        for year_val in [2007, 2008]:
            year_dates = [d for d in dates if d.year == year_val]
            if not year_dates: continue
            
            data = []
            for date_val in year_dates:
                for mat_val in maturities:
                    # 简单的测试收益率：基础值 + 期限斜率 * 期限 + 日期效应 + 噪声
                    yield_val = 2.0 + 0.5 * mat_val + (date_val.month - 1) * 0.1 + np.random.rand() * 0.1
                    data.append({'date': date_val.strftime('%Y/%m/%d'), 'maturity': mat_val, 'yield': yield_val})
            
            df_dummy = pd.DataFrame(data)
            dummy_filename = os.path.join(current_dir, f"gz_spot_select_{year_val}.csv")
            df_dummy.to_csv(dummy_filename, index=False)
            print(f"已创建测试数据文件: {dummy_filename}")
    
    # 设置DATA_PATH_PATTERN为当前目录中的文件
    DATA_PATH_PATTERN = os.path.join(current_dir, "gz_spot_select_*.csv")
    
    # 执行主程序
    main()