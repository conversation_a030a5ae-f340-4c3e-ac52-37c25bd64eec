# -*- coding: utf-8 -*-
"""
收益率曲线主成分分析脚本

本脚本用于读取多个CSV文件中的利率期限结构数据，
进行预处理后，执行主成分分析（PCA），并绘制结果图表，
以复现或类似研报中的利率曲线主成分（水平、斜率、曲度）分析。
"""

import pandas as pd
import numpy as np
from sklearn.decomposition import PCA
import matplotlib.pyplot as plt
import os
import glob


DATA_DIR = './'

# 匹配数据CSV文件名的模式 (请根据您的实际情况修改)
# 例如 'yield_data_*.csv' 或 'rates_*.csv'
FILE_PATTERN = 'gz_spot_select_*.csv'
TARGET_MATURITIES = [0.25, 0.5, 0.75, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
YIELD_IS_PERCENTAGE = True

# 数据截止日期 (格式: 'YYYY-MM-DD')
CUT_OFF_DATE = '2019-09-30'

# Matplotlib 显示中文字体设置
CHINESE_FONT = 'SimHei'


# --- 1. 数据读取和预处理函数 ---
def load_and_preprocess_data(data_dir, file_pattern, target_maturities, yield_is_percentage=True, cut_off_date=None):
    """
    加载多个CSV文件，合并，并将长格式数据转换为宽格式。
    参数:
    data_dir (str): CSV文件所在的目录。
    file_pattern (str): 匹配CSV文件名的模式。
    target_maturities (list): 我们感兴趣的期限列表。
    yield_is_percentage (bool): 'yield' 列中的值是否是百分比形式。
    cut_off_date (str, optional): 数据截止日期，格式为'YYYY-MM-DD'。只分析该日期及之前的数据。

    返回:
    pandas.DataFrame: 预处理后的宽格式数据，行为日期，列为期限。
                        如果失败则返回空的DataFrame。
    """
    full_pattern = os.path.join(data_dir, file_pattern)
    all_files = glob.glob(full_pattern)
    all_files.sort()  # 确保按文件名（通常包含年份）顺序读取

    if not all_files:
        print(f"错误：在目录 '{data_dir}' 中未找到匹配 '{file_pattern}' 的文件。")
        print(f"完整搜索路径为: '{full_pattern}'")
        return pd.DataFrame()

    df_list = []
    print("信息：正在读取以下文件:")
    for filename in all_files:
        print(f" - {filename}")
        # 假设CSV文件编码为UTF-8，如果不是，可能需要指定 encoding 参数
        df_year = pd.read_csv(filename)
        # 根据用户截图，列名推断为 'date', 'maturity', 'yield'
        expected_columns = {'date', 'maturity', 'yield'}
        if not expected_columns.issubset(df_year.columns):
            print(f"警告：文件 {filename} 的列名不符合预期 (需要: {expected_columns})。")
            print(f"文件实际列名: {list(df_year.columns)}")
            print("请检查CSV文件的列名并相应修改代码中的列名引用。正在跳过此文件。")
            continue
        df_list.append(df_year)

    if not df_list:
        print("错误：未能成功读取任何有效的数据文件。")
        return pd.DataFrame()

    combined_df = pd.concat(df_list, ignore_index=True)
    print(f"信息：已合并 {len(df_list)} 个文件，共 {len(combined_df)} 行原始数据。")

    # 转换 'date' 列为 datetime 对象
    try:
        combined_df['date'] = pd.to_datetime(combined_df['date'])
    except Exception as e:
        print(f"错误：转换 'date' 列为日期格式时出错: {e}")
        print("请确保 'date' 列的格式可以被 pandas.to_datetime 正确解析（例如 'YYYY/MM/DD' 或 'YYYY-MM-DD'）。")
        return pd.DataFrame()

    # 如果指定了截止日期，则筛选出该日期及之前的数据
    if cut_off_date:
        try:
            cut_off_date_dt = pd.to_datetime(cut_off_date)
            original_rows = len(combined_df)
            combined_df = combined_df[combined_df['date'] <= cut_off_date_dt]
            filtered_rows = len(combined_df)
            if filtered_rows < original_rows:
                print(f"信息：根据截止日期 {cut_off_date} 筛选，数据行数从 {original_rows} 减少到 {filtered_rows}。")
            if combined_df.empty:
                print(f"错误：截止日期 {cut_off_date} 之前没有数据。请检查日期格式或调整截止日期。")
                return pd.DataFrame()
        except Exception as e:
            print(f"警告：截止日期 {cut_off_date} 格式无效或筛选过程出错: {e}")
            print("将使用所有可用数据继续分析。")

    # 如果收益率是百分比形式 (例如 1.89 而不是 0.0189)，则转换为小数
    if yield_is_percentage:
        combined_df['yield'] = combined_df['yield'] / 100.0

    # 数据透视：将长格式转换为宽格式
    # index 是日期，columns 是期限，values 是收益率
    try:
        yield_curve_df = combined_df.pivot_table(index='date', columns='maturity', values='yield')
    except Exception as e:
        print(f"错误：数据透视操作（pivot_table）失败: {e}")
        return pd.DataFrame()

    # 筛选我们感兴趣的期限，并确保它们存在
    available_maturities_in_data = [m for m in target_maturities if m in yield_curve_df.columns]
    missing_maturities = [m for m in target_maturities if m not in yield_curve_df.columns]

    if missing_maturities:
        print(f"警告：以下目标期限在数据中缺失，分析将仅针对存在的期限进行: {missing_maturities}")

    if not available_maturities_in_data:
        print("错误：所有目标期限均在数据中缺失，无法继续分析。")
        print(f"数据中可用的期限列为: {list(yield_curve_df.columns)}")
        return pd.DataFrame()

    yield_curve_df = yield_curve_df[available_maturities_in_data]
    print(f"信息：已筛选出以下期限进行分析: {available_maturities_in_data}")

    # 处理缺失值 (例如，某些日期可能缺少某些期限的数据)
    # 这里使用前向填充，然后后向填充，您也可以选择其他策略（如插值或删除）
    original_rows = len(yield_curve_df)
    yield_curve_df = yield_curve_df.ffill().bfill()
    yield_curve_df = yield_curve_df.dropna() # 删除仍然有NaN的行（如果首尾都是NaN）

    if len(yield_curve_df) < original_rows:
        print(f"信息：缺失值处理后，数据行数从 {original_rows} 减少到 {len(yield_curve_df)}。")

    if yield_curve_df.empty:
        print("错误：预处理后数据为空。请检查原始数据和处理步骤。")

    return yield_curve_df.sort_index() # 按日期排序


# --- 2. 主成分分析 (PCA) 和绘图函数 ---
def perform_pca_and_plot(rate_data_df, chinese_font='SimHei'):
    """
    对处理好的收益率数据进行PCA分析并绘制结果图。

    参数:
    rate_data_df (pandas.DataFrame): 宽格式的收益率数据。
    chinese_font (str): 用于Matplotlib显示中文的字体名称。
    """
    if rate_data_df.empty:
        print("错误：传入的收益率数据为空，无法进行PCA分析。")
        return

    print("\n--- 开始执行主成分分析 (PCA) ---")

    # 不再对数据进行标准化，直接使用原始数据
    # 初始化PCA，提取前3个主成分
    pca = PCA(n_components=3)

    # 对原始数据进行PCA拟合和转换
    principal_components = pca.fit_transform(rate_data_df)

    # 将主成分转换为DataFrame
    pc_df = pd.DataFrame(data=principal_components,
                        columns=['主成分1 (水平)', '主成分2 (斜率)', '主成分3 (曲度)'],
                        index=rate_data_df.index)

    # 分析解释方差
    explained_variance_ratio = pca.explained_variance_ratio_
    print(f"信息：各主成分解释的方差比例: {explained_variance_ratio}")
    print(f"信息：前3个主成分累计解释的方差比例: {np.sum(explained_variance_ratio):.4f}")

    # 获取主成分载荷 (系数)
    loadings = pca.components_
    loadings_df = pd.DataFrame(loadings.T,
                                columns=['主成分1 (水平)', '主成分2 (斜率)', '主成分3 (曲度)'],
                                index=rate_data_df.columns)
    print("\n信息：主成分载荷 (系数):")
    print(loadings_df)

    # --- 绘图 ---
    try:
        plt.rcParams['font.sans-serif'] = [chinese_font]
        plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题
    except Exception as e:
        print(f"警告：设置中文字体 '{chinese_font}' 失败: {e}")
        print("图表中的中文可能无法正常显示。请确保已安装该字体或更换为其他可用字体。")

    # 图1: 主成分的时间序列图
    plt.figure(figsize=(14, 7))
    for col in pc_df.columns:
        plt.plot(pc_df.index, pc_df[col], label=col)
    plt.title('主成分历史走势', fontsize=16)
    plt.xlabel('日期', fontsize=12)
    plt.ylabel('主成分值', fontsize=12)
    plt.legend(fontsize=10)
    plt.grid(True, linestyle='--', alpha=0.7)
    plt.tight_layout()
    plt.savefig('principal_components_timeseries.png') # 保存图像
    plt.show()
    print("信息：主成分时间序列图已显示并保存为 'principal_components_timeseries.png'")


    # 图2: 主成分载荷 (系数) 在不同期限上的分布
    plt.figure(figsize=(14, 7))

    # 确保载荷的横坐标是数值类型以便正确绘图和排序
    plot_loadings_df = loadings_df.copy()
    try:
        plot_loadings_df.index = plot_loadings_df.index.astype(float)
        plot_loadings_df = plot_loadings_df.sort_index()
        x_ticks_values = plot_loadings_df.index
    except ValueError:
        print("警告：期限（载荷图的X轴）无法转换为数值类型，将使用原始索引绘图。")
        x_ticks_values = rate_data_df.columns # 使用原始列名作为刻度

    for col in plot_loadings_df.columns:
        plt.plot(plot_loadings_df.index, plot_loadings_df[col], marker='o', linestyle='-', label=col)

    plt.title('主成分载荷 (系数) 与期限的关系', fontsize=16)
    plt.xlabel('期限 (年)', fontsize=12)
    plt.ylabel('载荷 (系数值)', fontsize=12)
    if isinstance(x_ticks_values, pd.Index) and pd.api.types.is_numeric_dtype(x_ticks_values):
        plt.xticks(ticks=x_ticks_values, labels=[str(round(val,2)) for val in x_ticks_values], rotation=45, ha="right")
    else:
        plt.xticks(rotation=45, ha="right")

    plt.axhline(0, color='black', linewidth=0.8, linestyle='--')
    plt.legend(fontsize=10)
    plt.grid(True, linestyle='--', alpha=0.7)
    plt.tight_layout()
    plt.savefig('principal_components_loadings.png') # 保存图像
    plt.show()
    print("信息：主成分载荷图已显示并保存为 'principal_components_loadings.png'")

    print("\n--- 分析说明 ---")
    print("1. 水平因子 (主成分1): 通常所有期限的载荷具有相同符号且数值相近，代表利率曲线的整体平行移动。")
    print("2. 斜率因子 (主成分2): 通常短端和长端载荷符号相反，代表利率曲线斜率（利差）的变化。")
    print("3. 曲度因子 (主成分3): 通常中端载荷与两端载荷符号相反，代表利率曲线弯曲程度的变化。")
    print("注意：主成分及其载荷的绝对符号（正或负）可以任意翻转，重要的是其相对模式和解释的方差。")


# --- 3. 主程序执行流程 ---
def main():
    """
    主程序，执行数据加载、PCA分析和绘图。
    """
    print("--- 收益率曲线主成分分析程序启动 ---")
    print(f"当前工作目录: {os.getcwd()}")
    print(f"数据目录: {os.path.abspath(DATA_DIR)}")
    print(f"文件匹配模式: {FILE_PATTERN}")
    print(f"目标分析期限: {TARGET_MATURITIES}")
    print(f"收益率是否为百分比形式: {YIELD_IS_PERCENTAGE}")
    print(f"数据截止日期: {CUT_OFF_DATE}")
    print(f"图表使用中文字体: {CHINESE_FONT}")

    # 加载和预处理数据
    your_rate_data = load_and_preprocess_data(DATA_DIR, FILE_PATTERN, TARGET_MATURITIES, YIELD_IS_PERCENTAGE, CUT_OFF_DATE)

    if your_rate_data.empty:
        print("\n错误：数据加载和预处理失败，程序终止。请检查上述错误信息。")
        return
    else:
        print("\n信息：数据加载和预处理成功。数据预览 (前5行):")
        print(your_rate_data.head())
        print(f"数据形状 (行数, 列数/期限数): {your_rate_data.shape}")

        # 执行PCA分析和绘图
        perform_pca_and_plot(your_rate_data, CHINESE_FONT)

    print("\n--- 程序执行完毕 ---")

if __name__ == '__main__':
    main()