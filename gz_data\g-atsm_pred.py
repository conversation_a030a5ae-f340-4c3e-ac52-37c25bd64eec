import pandas as pd
import numpy as np
from sklearn.decomposition import PCA
import statsmodels.api as sm  # For VAR
import os
import matplotlib.pyplot as plt

# 假设 utils.py 文件在同一目录下
try:
    from utils import (load_and_prepare_data_for_modeling, calculate_oos_r_squared,
                       calculate_hit_ratio, diebold_mariano_test, create_dummy_data_if_not_exists,
                       CHINESE_FONT, DATA_PATH_PATTERN)
except ImportError:
    print("错误：无法导入utils.py。")
    exit()

# 从 G-ATSM.py 引入必要的函数 (你需要确保这些函数可以被外部调用，并且接口清晰)
# 例如:
# from G_ATSM import get_state_variables, estimate_atsm_parameters, calculate_no_arbitrage_yields
# 为了简单起见，我们这里假设这些函数会被修改或包装在此文件内。

# --- 模型参数 ---
TARGET_MATURITIES_FOR_PREDICTION = [0.25, 1, 3, 5, 7, 10]  # 要预测的特定期限（年）
PREDICTION_HORIZON_MONTHS = [3, 6, 12]  # 预测未来h个月
OUTPUT_DIR = "model_results"
GATSM_RESULTS_FILE = os.path.join(OUTPUT_DIR, "gatsm_predictions.csv")

K_FACTORS = 3  # 与 G-ATSM.py 中的一致
DT = 1 / 12  # 时间步长 (月度数据)


# SHORT_RATE_MATURITY_YEARS = 1 # 来自G-ATSM.py，短期利率期限，用于估计delta0, delta1

# --- G-ATSM Core Functions (Adapted or simplified from your G-ATSM.py) ---
# 你需要将 G-ATSM.py 中的相关函数（PCA、参数估计、无套利收益计算）整合进来
# 并调整它们以适应扩张窗口的预测设置。

def get_state_variables_for_prediction(yield_df_train, k_factors):
    """
    对训练集收益率数据进行PCA。返回PCA模型和转换后的状态变量。
    注意：PCA应该在每个扩张窗口的训练集上fit，然后transform该训练集和对应的测试点。
    或者，一种简化方法是在初始训练集上fit PCA，然后用这个PCA模型transform后续所有数据，
    但这可能不是严格的扩张窗口。
    研报通常在每个时间点重新估计整个模型，包括PCA。
    """
    if yield_df_train.empty or yield_df_train.shape[1] < k_factors:
        print("警告: PCA的输入数据不足。")
        return None, pd.DataFrame(columns=[f'PC{i + 1}' for i in range(k_factors)])

    pca = PCA(n_components=k_factors)
    # 标准化通常在PCA之前完成
    yield_standardized = (yield_df_train - yield_df_train.mean()) / yield_df_train.std()
    yield_standardized = yield_standardized.dropna(axis=1, how='all')  # 处理std为0的情况
    if yield_standardized.shape[1] < k_factors:
        print(f"警告: 标准化后可用期限 ({yield_standardized.shape[1]}) 少于 k_factors ({k_factors})。")
        return None, pd.DataFrame(columns=[f'PC{i + 1}' for i in range(k_factors)])

    X_t_train = pca.fit_transform(yield_standardized)
    X_t_train_df = pd.DataFrame(X_t_train, index=yield_df_train.index,
                                columns=[f'PC{i + 1}' for i in range(k_factors)])
    return pca, X_t_train_df, yield_df_train.mean(), yield_df_train.std()  # 返回均值和标准差用于转换测试点


def estimate_var1_params(X_t_train_df):
    """估计VAR(1)参数: X_{t+1} = mu + Phi * X_t + v_{t+1}"""
    X = X_t_train_df.values
    X_lagged = X[:-1, :]
    X_current = X[1:, :]
    X_lagged_with_const = sm.add_constant(X_lagged)

    if X_lagged_with_const.shape[0] < X_lagged_with_const.shape[1]:  # 数据点少于参数个数
        print("警告: VAR(1)估计中数据点不足。")
        return None, None, None

    try:
        # OLS for each component of X_current
        phi_coeffs_lst = []
        v_residuals_lst = []
        for k_col in range(X_current.shape[1]):
            model_var = sm.OLS(X_current[:, k_col], X_lagged_with_const)
            results_var = model_var.fit()
            phi_coeffs_lst.append(results_var.params)
            v_residuals_lst.append(results_var.resid)

        phi_coeffs_stacked = np.array(phi_coeffs_lst).T
        mu_p_var = phi_coeffs_stacked[0, :].reshape(-1, 1)
        Phi_p_var = phi_coeffs_stacked[1:, :]

        v_residuals = np.array(v_residuals_lst).T
        Sigma_var = np.cov(v_residuals, rowvar=False)
    except Exception as e:
        print(f"VAR(1)参数估计出错: {e}")
        return None, None, None

    return mu_p_var, Phi_p_var, Sigma_var


def estimate_short_rate_params(X_t_train_df, yields_df_train, short_rate_maturity_actual, dt):
    """估计短期利率参数: r_t = delta0 + delta1' * X_t"""
    short_rate_series = yields_df_train.get(short_rate_maturity_actual)
    if short_rate_series is None:
        # 尝试找到最接近的期限作为短期利率
        available_mats = np.array(yields_df_train.columns)
        closest_mat = available_mats[np.abs(available_mats - short_rate_maturity_actual).argmin()]
        print(f"警告：G-ATSM中未找到精确短期利率期限 {short_rate_maturity_actual}。使用最接近的: {closest_mat}")
        short_rate_series = yields_df_train[closest_mat]
        # global SHORT_RATE_MATURITY_YEARS_ACTUAL # 如果要全局修改
        # SHORT_RATE_MATURITY_YEARS_ACTUAL = closest_mat

    short_rate_annual = short_rate_series.loc[X_t_train_df.index].values  # 对齐
    short_rate_period = short_rate_annual * dt

    X_with_const_sr = sm.add_constant(X_t_train_df.values)

    if short_rate_period.shape[0] != X_with_const_sr.shape[0]:  # 检查对齐
        print("警告: 短期利率和状态变量长度不匹配。")
        min_len = min(len(short_rate_period), len(X_with_const_sr))
        short_rate_period = short_rate_period[:min_len]
        X_with_const_sr = X_with_const_sr[:min_len, :]

    try:
        model_sr = sm.OLS(short_rate_period, X_with_const_sr)
        results_sr = model_sr.fit()
        delta0_period = results_sr.params[0]
        delta1_period = results_sr.params[1:].reshape(-1, 1)
    except Exception as e:
        print(f"短期利率参数估计出错: {e}")
        return None, None

    return delta0_period, delta1_period


# ... 需要 lambda0, lambda1 的估计 (来自 G-ATSM.py OLS 3) ...
# ... 以及 A_n, B_n 的计算 (来自 G-ATSM.py calculate_no_arbitrage_yields) ...
# 这些函数需要被适配以在扩张窗口的每次迭代中被调用

def estimate_risk_price_params(v_residuals, X_lagged_for_lambda, Sigma_var, k_factors):
    """
    估计风险价格参数 lambda0, lambda1.
    v_residuals: 来自 VAR(1) 的残差 (v_{t+1})
    X_lagged_for_lambda: 对应于 v_residuals 的 X_t
    Sigma_var: VAR(1) 残差的协方差矩阵
    """
    if v_residuals is None or X_lagged_for_lambda is None or Sigma_var is None:
        return None, None

    if v_residuals.shape[0] != X_lagged_for_lambda.shape[0]:
        print("警告: 风险价格参数估计中，残差和X_lagged长度不匹配。")
        return None, None

    X_lagged_with_const = sm.add_constant(X_lagged_for_lambda)

    b_coeffs_lst = []
    try:
        for k in range(k_factors):
            model_lambda_reg = sm.OLS(v_residuals[:, k], X_lagged_with_const)
            results_lambda_reg = model_lambda_reg.fit()
            b_coeffs_lst.append(results_lambda_reg.params)
    except Exception as e:
        print(f"风险价格回归系数估计出错: {e}")
        return None, None

    b_coeffs_stacked = np.array(b_coeffs_lst).T

    neg_Sigma_lambda0_T = b_coeffs_stacked[0, :]
    neg_Sigma_lambda1_T = b_coeffs_stacked[1:, :]

    try:
        Sigma_inv = np.linalg.inv(Sigma_var)
        lambda0 = -Sigma_inv @ neg_Sigma_lambda0_T.reshape(k_factors, 1)
        lambda1 = -Sigma_inv @ neg_Sigma_lambda1_T.T  # ( (Sigma_lambda1_T)' = lambda1'Sigma ) -> Sigma_inv @ (-neg_Sigma_lambda1_T).T
    except np.linalg.LinAlgError:
        print("警告: Sigma_var 奇异，无法计算风险价格参数。")
        return None, None

    return lambda0, lambda1


def calculate_A_B_coeffs(max_maturity_periods, k_factors, mu_q, Phi_q, Sigma_var, delta0_period, delta1_period):
    """计算无套利模型中的 A_n 和 B_n 系数"""
    A_coeffs = np.zeros(max_maturity_periods + 1)
    B_coeffs = np.zeros((max_maturity_periods + 1, k_factors))

    for n_p in range(max_maturity_periods):
        term_B_Sigma_B = B_coeffs[n_p, :] @ Sigma_var @ B_coeffs[n_p, :].T
        A_coeffs[n_p + 1] = A_coeffs[n_p] + B_coeffs[n_p, :] @ mu_q + delta0_period - 0.5 * term_B_Sigma_B
        B_coeffs[n_p + 1, :] = B_coeffs[n_p, :] @ Phi_q + delta1_period.T
        if B_coeffs[n_p + 1, :].ndim > 1 and B_coeffs[n_p + 1, :].shape[0] > 1:
            B_coeffs[n_p + 1, :] = B_coeffs[n_p + 1, :].flatten()
    return A_coeffs, B_coeffs


def predict_yields_with_gatsm(X_t_plus_h_pred, A_coeffs, B_coeffs, target_model_maturities_years, dt):
    """使用预测的状态变量和A,B系数来预测收益率"""
    predicted_yields = {}  # maturity_year -> predicted_yield_at_t_plus_h
    X_pred_reshaped = X_t_plus_h_pred.reshape(1, -1)  # 确保是 (1, K_FACTORS)

    for mat_yr in target_model_maturities_years:
        n_periods = int(round(mat_yr / dt))
        if n_periods <= 0 or n_periods >= len(A_coeffs):
            # print(f"  GATSM: 期限 {mat_yr} (周期 {n_periods}) 超出A,B系数范围，跳过。")
            continue

        A_n = A_coeffs[n_periods]
        B_n = B_coeffs[n_periods, :].reshape(K_FACTORS, 1)

        model_yield_period_rate = - (A_n / n_periods) - (X_pred_reshaped @ B_n / n_periods).flatten()[0]
        model_yield_annualized = model_yield_period_rate / dt
        predicted_yields[mat_yr] = model_yield_annualized
    return predicted_yields


def run_gatsm_prediction_model(yield_data_full,  # 包含所有期限用于PCA和参数估计
                               target_pred_maturities,  # 我们要预测其变化的期限
                               horizons_months):
    print("\n--- 运行 G-ATSM 预测模型 ---")
    os.makedirs(OUTPUT_DIR, exist_ok=True)

    all_gatsm_predictions = []
    results_summary = []

    # 确定短期利率的实际使用期限 (从 G-ATSM.py 的 load_and_prepare_data 逻辑)
    # 简单处理：假设数据中存在1年期，或者选择最接近1年期的
    if 1.0 in yield_data_full.columns:
        short_rate_maturity_actual_for_model = 1.0
    else:
        available_mats_model = np.array(yield_data_full.columns)
        short_rate_maturity_actual_for_model = available_mats_model[np.abs(available_mats_model - 1.0).argmin()]
        print(f"G-ATSM: 短期利率使用 {short_rate_maturity_actual_for_model} 年 (最接近1年)。")

    for h_months in horizons_months:
        print(f"\n预测窗口: {h_months} 个月")
        h_steps = h_months

        for mat_target_pred in target_pred_maturities:
            if mat_target_pred not in yield_data_full.columns:
                print(f"警告：目标预测期限 {mat_target_pred} 不在数据中，跳过。")
                continue

            print(f"  处理目标预测期限: {mat_target_pred} 年")

            # 扩张窗口逻辑
            min_train_size_gatsm = 60  # G-ATSM可能需要更长的初始窗口, e.g., 5 years

            y_preds_gatsm_list = []  # 存储 Δy_pred
            y_true_change_list = []  # 存储 Δy_actual
            dates_list = []

            # 迭代的 `current_t_idx` 是指当前时间点 t 在 `yield_data_full` 中的行索引
            # 我们将使用 `0` 到 `current_t_idx` 的数据来训练模型
            # 并预测 `current_t_idx + h_steps` 的利率变化

            num_possible_predictions = len(yield_data_full) - min_train_size_gatsm - h_steps + 1
            if num_possible_predictions <= 0:
                print(
                    f"    期限 {mat_target_pred}，窗口 {h_months}m：数据不足 ({len(yield_data_full)}) 进行GATSM扩张窗口。")
                continue

            print(f"    开始G-ATSM扩张窗口训练与预测 (共 {num_possible_predictions} 个预测点)...")

            for i in range(num_possible_predictions):
                current_t_numeric_idx = min_train_size_gatsm + i - 1
                predict_for_date_idx_numeric = current_t_numeric_idx + h_steps

                if predict_for_date_idx_numeric >= len(yield_data_full):
                    break  # 超出数据范围

                # 1. 准备当前扩张窗口的训练数据 (直到 current_t_numeric_idx)
                current_train_data = yield_data_full.iloc[:current_t_numeric_idx + 1]
                current_t_date = yield_data_full.index[current_t_numeric_idx]

                # 2. 在 current_train_data 上执行G-ATSM的参数估计步骤
                #   a. PCA
                pca_model, X_t_train_df, mean_pca, std_pca = get_state_variables_for_prediction(current_train_data,
                                                                                                K_FACTORS)
                if pca_model is None or X_t_train_df.empty:
                    print(f"      在 {current_t_date}: PCA失败，跳过。")
                    continue

                #   b. VAR(1) for X_t
                mu_p_var, Phi_p_var, Sigma_var = estimate_var1_params(X_t_train_df)
                if mu_p_var is None:
                    print(f"      在 {current_t_date}: VAR参数估计失败，跳过。")
                    continue

                #   c. Short rate params (delta0, delta1)
                delta0_p, delta1_p = estimate_short_rate_params(X_t_train_df, current_train_data,
                                                                short_rate_maturity_actual_for_model, DT)
                if delta0_p is None:
                    print(f"      在 {current_t_date}: 短期利率参数估计失败，跳过。")
                    continue

                #   d. Risk prices (lambda0, lambda1) - 需要VAR的残差和X_lagged
                var_residuals = X_t_train_df.values[1:] - (
                            sm.add_constant(X_t_train_df.values[:-1]) @ np.vstack((mu_p_var.T, Phi_p_var))).values
                X_lagged_for_lambda_est = X_t_train_df.values[:-1]

                lambda0, lambda1 = estimate_risk_price_params(var_residuals, X_lagged_for_lambda_est, Sigma_var,
                                                              K_FACTORS)
                if lambda0 is None:
                    print(f"      在 {current_t_date}: 风险价格参数估计失败，跳过。")
                    continue

                #   e. Q-measure params
                mu_q = mu_p_var - Sigma_var @ lambda0
                Phi_q = Phi_p_var - Sigma_var @ lambda1

                #   f. A_n, B_n coefficients
                max_mat_periods = int(round(np.max(yield_data_full.columns) / DT))
                A_coeffs, B_coeffs = calculate_A_B_coeffs(max_mat_periods, K_FACTORS, mu_q, Phi_q, Sigma_var, delta0_p,
                                                          delta1_p)

                # 3. 预测未来 h_steps 期的状态变量 X_{t+h}
                X_t_current_val = X_t_train_df.iloc[-1].values.reshape(K_FACTORS, 1)  # Last X_t from training
                X_t_plus_h_forecast = X_t_current_val.copy()
                for _ in range(h_steps):
                    X_t_plus_h_forecast = mu_p_var + Phi_p_var @ X_t_plus_h_forecast

                # 4. 使用预测的 X_{t+h} 和估计的 A,B 系数来预测 y_{t+h}(tau)
                # 预测所有目标期限在 t+h 的利率
                predicted_yields_at_t_plus_h = predict_yields_with_gatsm(
                    X_t_plus_h_forecast, A_coeffs, B_coeffs, target_pred_maturities, DT
                )

                if mat_target_pred not in predicted_yields_at_t_plus_h:
                    # print(f"      在 {current_t_date} for H={h_months}m: 未能预测期限 {mat_target_pred} 的收益率。")
                    continue  # Skip if target maturity wasn't predicted

                y_t_plus_h_pred_gatsm = predicted_yields_at_t_plus_h[mat_target_pred]

                # 5. 获取实际值并计算变化
                y_t_actual = yield_data_full.loc[current_t_date, mat_target_pred]

                actual_future_date = yield_data_full.index[predict_for_date_idx_numeric]
                y_t_plus_h_actual = yield_data_full.loc[actual_future_date, mat_target_pred]

                delta_y_actual_val = y_t_plus_h_actual - y_t_actual
                delta_y_pred_gatsm_val = y_t_plus_h_pred_gatsm - y_t_actual

                y_preds_gatsm_list.append(delta_y_pred_gatsm_val)
                y_true_change_list.append(delta_y_actual_val)
                dates_list.append(current_t_date)  # 预测是在 t 时点做出的，针对 t+h

            if not y_preds_gatsm_list:
                print(f"    期限 {mat_target_pred}，窗口 {h_months} 个月：没有生成任何G-ATSM预测。")
                continue

            y_true_change_eval = np.array(y_true_change_list)
            y_pred_gatsm_change_eval = np.array(y_preds_gatsm_list)

            # 评估
            mse_gatsm = mean_squared_error(y_true_change_eval, y_pred_gatsm_change_eval)
            oos_r2_gatsm = calculate_oos_r_squared(y_true_change_eval, y_pred_gatsm_change_eval, 0)  # Vs RW
            hit_ratio_gatsm = calculate_hit_ratio(y_true_change_eval, y_pred_gatsm_change_eval)

            print(f"    MSE (G-ATSM): {mse_gatsm:.6g}")
            print(f"    OOS R^2 (G-ATSM vs. RW): {oos_r2_gatsm:.4f}")
            print(f"    Hit Ratio (G-ATSM): {hit_ratio_gatsm:.4f}")

            errors_gatsm = y_true_change_eval - y_pred_gatsm_change_eval
            errors_rw_gatsm = y_true_change_eval - 0
            if len(errors_gatsm) > h_steps:
                dm_stat_g, p_value_g = diebold_mariano_test(errors_gatsm, errors_rw_gatsm, h=h_steps)
                print(f"    Diebold-Mariano Test (GATSM vs RW): DM Stat={dm_stat_g:.4f}, p-value={p_value_g:.4f}")
            else:
                dm_stat_g, p_value_g = np.nan, np.nan

            # 保存结果
            # y_t, y_t+h_actual, y_t+h_pred
            y_t_vals_output = yield_data_full.loc[dates_list, mat_target_pred].values
            y_t_plus_h_actual_output = y_t_vals_output + y_true_change_eval
            y_t_plus_h_pred_gatsm_output = y_t_vals_output + y_pred_gatsm_change_eval

            predictions_df_curr = pd.DataFrame({
                'date': dates_list,
                'maturity': mat_target_pred,
                'horizon_months': h_months,
                'y_t': y_t_vals_output,
                'y_t_plus_h_actual': y_t_plus_h_actual_output,
                'delta_y_actual': y_true_change_eval,
                'delta_y_pred_gatsm': y_pred_gatsm_change_eval,
                'y_t_plus_h_pred_gatsm': y_t_plus_h_pred_gatsm_output
            })
            all_gatsm_predictions.append(predictions_df_curr)

            results_summary.append({
                'model': 'GATSM',
                'maturity': mat_target_pred,
                'horizon_months': h_months,
                'MSE': mse_gatsm,
                'OOS_R2_vs_RW': oos_r2_gatsm,
                'HitRatio': hit_ratio_gatsm,
                'DM_stat_vs_RW': dm_stat_g,
                'DM_p_value_vs_RW': p_value_g
            })

    if not all_gatsm_predictions:
        print("错误：没有生成任何G-ATSM预测结果。")
        return pd.DataFrame(), pd.DataFrame()

    all_gatsm_final_df = pd.concat(all_gatsm_predictions, ignore_index=True)
    all_gatsm_final_df.to_csv(GATSM_RESULTS_FILE, index=False, encoding='utf-8-sig')
    print(f"\nG-ATSM模型预测结果已保存到: {GATSM_RESULTS_FILE}")

    summary_df = pd.DataFrame(results_summary)
    print("\nG-ATSM模型评估摘要:")
    print(summary_df)
    return all_gatsm_final_df, summary_df


if __name__ == "__main__":
    create_dummy_data_if_not_exists(DATA_PATH_PATTERN, current_dir=".")

    try:
        # G-ATSM 通常在所有可用期限上进行PCA和参数估计
        yield_panel_all_maturities = load_and_prepare_data_for_modeling(
            DATA_PATH_PATTERN,
            yield_is_percentage=YIELD_IS_PERCENTAGE,
            selected_maturities=None  # Load all available for PCA etc.
        )
    except Exception as e:
        print(f"数据加载失败: {e}")
        exit()

    if yield_panel_all_maturities.empty or yield_panel_all_maturities.shape[1] < K_FACTORS:
        print(f"加载的数据为空或期限数少于K_FACTORS({K_FACTORS})，无法运行G-ATSM。")
        exit()

    gatsm_predictions, gatsm_summary = run_gatsm_prediction_model(
        yield_panel_all_maturities,
        TARGET_MATURITIES_FOR_PREDICTION,  # These are the specific maturities whose changes we want to predict
        PREDICTION_HORIZON_MONTHS
    )

    if not gatsm_predictions.empty:
        sample_mat_g = TARGET_MATURITIES_FOR_PREDICTION[0]
        sample_hor_g = PREDICTION_HORIZON_MONTHS[0]
        plot_data_g = gatsm_predictions[
            (gatsm_predictions['maturity'] == sample_mat_g) &
            (gatsm_predictions['horizon_months'] == sample_hor_g)
            ]

        if not plot_data_g.empty:
            plt.figure(figsize=(12, 6))
            plt.plot(plot_data_g['date'], plot_data_g['delta_y_actual'], label=f'Actual Change (Δy_t+{sample_hor_g}m)',
                     alpha=0.7)
            plt.plot(plot_data_g['date'], plot_data_g['delta_y_pred_gatsm'], label=f'G-ATSM Predicted Change',
                     linestyle='--')
            plt.axhline(0, color='red', linestyle=':', label='Random Walk Predicted Change (0)')
            plt.title(f'G-ATSM: Maturity {sample_mat_g}yr, Horizon {sample_hor_g}m - Interest Rate Changes')
            plt.xlabel('Date (Prediction made at this date for t+h)')
            plt.ylabel('Interest Rate Change (Δy)')
            plt.legend()
            plt.grid(True)
            plt.tight_layout()
            plt.savefig(os.path.join(OUTPUT_DIR, f"gatsm_delta_y_plot_M{sample_mat_g}_H{sample_hor_g}.png"))
            plt.show()